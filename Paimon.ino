#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>

const char* ssid = "Redmi Note 12 5G";
const char* password = "123456789";

ESP8266WebServer server(80);

void handleRoot() {
  String html = "<!DOCTYPE html><html lang='es'><head>";
  html += "<meta charset='UTF-8'>";
  html += "<title>ESP8266 - Simulación Física y Doom 3D</title>";
  html += "<style>body{background:linear-gradient(45deg,#1a1a2e,#16213e,#0f3460);color:#eee;font-family:sans-serif;animation:bgShift 10s infinite;}@keyframes bgShift{0%,100%{background:linear-gradient(45deg,#1a1a2e,#16213e,#0f3460);}50%{background:linear-gradient(45deg,#0f3460,#16213e,#1a1a2e);}}h1{color:#ff9800;text-align:center;text-shadow:0 0 20px #ff9800,0 0 40px #ff9800;animation:glow 2s ease-in-out infinite alternate;}@keyframes glow{from{text-shadow:0 0 20px #ff9800,0 0 40px #ff9800;}to{text-shadow:0 0 30px #ff9800,0 0 60px #ff9800,0 0 80px #ff9800;}}#menu{display:flex;justify-content:center;margin:20px 0;}#menu button{background:linear-gradient(45deg,#ff9800,#ff5722);color:#222;padding:10px 20px;margin:0 10px;border:none;border-radius:10px;cursor:pointer;font-size:1em;box-shadow:0 4px 15px rgba(255,152,0,0.3);transition:all 0.3s ease;transform:perspective(1000px) rotateX(0deg);}#menu button:hover{transform:perspective(1000px) rotateX(-10deg) translateY(-5px);box-shadow:0 8px 25px rgba(255,152,0,0.5);}#menu button.active{background:linear-gradient(45deg,#f44336,#e91e63);color:#fff;box-shadow:0 6px 20px rgba(244,67,54,0.4);}#panel{margin:10px auto;text-align:center;background:rgba(255,255,255,0.1);backdrop-filter:blur(10px);border-radius:15px;padding:15px;border:1px solid rgba(255,255,255,0.2);}#panel input{margin:0 5px;accent-color:#ff9800;}#info{color:#ffd600;text-align:center;margin-top:10px;font-weight:bold;text-shadow:0 0 10px #ffd600;}canvas{display:block;margin:30px auto;background:radial-gradient(circle at center,#1a1a1a,#000);border:3px solid #ff9800;border-radius:10px;box-shadow:0 0 30px rgba(255,152,0,0.5),inset 0 0 50px rgba(0,0,0,0.8);}#doom3d{display:none;}#sim2d{display:none;}#minimap{position:absolute;top:30px;right:30px;background:rgba(17,17,17,0.9);border:2px solid #fff;border-radius:10px;opacity:0.9;box-shadow:0 0 20px rgba(255,255,255,0.3);}</style>";
  html += "</head><body>";
  html += "<h1>ESP8266 - Simulación Física y Doom 3D</h1>";
  html += "<div id='menu'>";
  html += "<button id='btn2d' class='active'>Simulación Física</button>";
  html += "<button id='btn3d'>Doom 3D</button>";
  html += "</div>";

  // Simulación Física 2D
  html += "<div id='sim2d'>";
  html += "<div id='panel'>";
  html += "Gravedad: <input type='range' id='grav' min='0.1' max='2.0' step='0.01' value='0.4'> <span id='gval'>0.40</span> ";
  html += "Fricción suelo: <input type='range' id='fric' min='0.5' max='1.0' step='0.01' value='0.8'> <span id='fval'>0.80</span> ";
  html += "Fricción aire: <input type='range' id='air' min='0.98' max='1.0' step='0.0005' value='0.995'> <span id='aval'>0.995</span> ";
  html += "</div>";
  html += "<canvas id='sim' width='400' height='260'></canvas>";
  html += "<div id='info'></div>";
  html += "</div>";

  // Doom 3D
  html += "<div id='doom3d' style='position:relative;'>";
  html += "<canvas id='doom' width='800' height='600'></canvas>";
  html += "<canvas id='minimap' width='120' height='110'></canvas>";
  html += "<div style='text-align:center;margin-top:10px;'>";
  html += "<span id='doominfo'></span><br>";
  html += "<button id='lockMouse' style='margin:10px;padding:10px 20px;background:#ff9800;border:none;border-radius:5px;cursor:pointer;'>Bloquear Mouse (Click para jugar)</button>";
  html += "</div>";
  html += "<div id='victory' style='display:none;text-align:center;font-size:2em;color:#ffd600;margin-top:30px;'>¡Victoria! Has escapado del laberinto.</div>";
  html += "</div>";

  html += "<script>\n";
  // --- Menú de pestañas ---
  html += "const btn2d=document.getElementById('btn2d'),btn3d=document.getElementById('btn3d');\n";
  html += "const sim2d=document.getElementById('sim2d'),doom3d=document.getElementById('doom3d');\n";
  html += "btn2d.onclick=()=>{btn2d.classList.add('active');btn3d.classList.remove('active');sim2d.style.display='block';doom3d.style.display='none';};\n";
  html += "btn3d.onclick=()=>{btn3d.classList.add('active');btn2d.classList.remove('active');sim2d.style.display='none';doom3d.style.display='block';};\n";
  html += "sim2d.style.display='block';\n";

  // --- Simulación Física 2D (igual que antes, omito aquí por espacio) ---
  html += "const canvas = document.getElementById('sim');\n";
  html += "const ctx = canvas.getContext('2d');\n";
  html += "let g = 0.4, friction = 0.8, air = 0.995;\n";
  html += "let balls = [], particles = [], trails = [], explosions = [], screenShake = 0;\n";
  html += "const colors = ['#ff9800','#f44336','#2196f3','#4caf50','#ffd600','#e040fb','#00bcd4','#ff5722','#cddc39','#607d8b'];\n";
  html += "const glowColors = ['#ffcc80','#ffcdd2','#bbdefb','#c8e6c9','#fff9c4','#f8bbd9','#b2ebf2','#ffccbc','#f0f4c3','#cfd8dc'];\n";
  html += "function Ball(x, y, r, color) {\n";
  html += "  return {x, y, r, color, vx: (Math.random()-0.5)*6, vy: (Math.random()-0.5)*6, mass: r*r/400, dragging: false, trail: [], energy: 1, glow: 0};\n";
  html += "}\n";
  html += "function Particle(x, y, vx, vy, color, life) {\n";
  html += "  return {x, y, vx, vy, color, life, maxLife: life, size: Math.random()*3+1, alpha: 1, gravity: Math.random()*0.1+0.05};\n";
  html += "}\n";
  html += "function Explosion(x, y, intensity) {\n";
  html += "  return {x, y, intensity, maxIntensity: intensity, particles: [], time: 0};\n";
  html += "}\n";
  html += "for(let i=0;i<15;i++){\n";
  html += "  balls.push(Ball(Math.random()*360+20,Math.random()*100+30,Math.random()*15+10,colors[i%colors.length]));\n";
  html += "}\n";
  html += "let dragging = null, offsetX = 0, offsetY = 0;\n";
  html += "function distance(x1, y1, x2, y2) { return Math.sqrt((x1-x2)**2 + (y1-y2)**2); }\n";
  html += "function resolveCollisions() {\n";
  html += "  for(let i=0;i<balls.length;i++){\n";
  html += "    for(let j=i+1;j<balls.length;j++){\n";
  html += "      let a=balls[i],b=balls[j];\n";
  html += "      let dx=b.x-a.x, dy=b.y-a.y, dist=distance(a.x,a.y,b.x,b.y);\n";
  html += "      let minDist = a.r + b.r;\n";
  html += "      if(dist<minDist && dist>0.01){\n";
  html += "        // Crear efecto de colisión\n";
  html += "        let impactForce = Math.sqrt(a.vx*a.vx + a.vy*a.vy + b.vx*b.vx + b.vy*b.vy);\n";
  html += "        if(impactForce > 3) {\n";
  html += "          explosions.push(Explosion((a.x+b.x)/2, (a.y+b.y)/2, impactForce*2));\n";
  html += "          screenShake = Math.min(10, impactForce);\n";
  html += "          a.glow = b.glow = 30;\n";
  html += "          // Partículas de chispa\n";
  html += "          for(let k=0;k<Math.min(8,impactForce);k++){\n";
  html += "            let angle = Math.random()*Math.PI*2;\n";
  html += "            particles.push(Particle((a.x+b.x)/2, (a.y+b.y)/2, Math.cos(angle)*impactForce*0.5, Math.sin(angle)*impactForce*0.5, '#fff', 30));\n";
  html += "          }\n";
  html += "        }\n";
  html += "        // Separar pelotas\n";
  html += "        let overlap = 0.6*(minDist - dist);\n";
  html += "        let nx=dx/dist, ny=dy/dist;\n";
  html += "        a.x -= overlap*nx*(b.mass/(a.mass+b.mass)); a.y -= overlap*ny*(b.mass/(a.mass+b.mass));\n";
  html += "        b.x += overlap*nx*(a.mass/(a.mass+b.mass)); b.y += overlap*ny*(a.mass/(a.mass+b.mass));\n";
  html += "        // Choque elástico 2D\n";
  html += "        let dvx = b.vx - a.vx;\n";
  html += "        let dvy = b.vy - a.vy;\n";
  html += "        let vn = dvx*nx + dvy*ny;\n";
  html += "        if(vn < 0){\n";
  html += "          let impulse = -1.2 * vn / (1/a.mass + 1/b.mass);\n";
  html += "          a.vx -= (impulse*nx)/a.mass; a.vy -= (impulse*ny)/a.mass;\n";
  html += "          b.vx += (impulse*nx)/b.mass; b.vy += (impulse*ny)/b.mass;\n";
  html += "        }\n";
  html += "      }\n";
  html += "    }\n";
  html += "  }\n";
  html += "}\n";
  html += "function update() {\n";
  html += "  // Actualizar pelotas\n";
  html += "  for(let b of balls){\n";
  html += "    if(!b.dragging){\n";
  html += "      // Agregar posición actual al trail\n";
  html += "      let speed = Math.sqrt(b.vx*b.vx + b.vy*b.vy);\n";
  html += "      if(speed > 1) {\n";
  html += "        b.trail.push({x: b.x, y: b.y, alpha: 1, size: b.r * (speed/10)});\n";
  html += "        if(b.trail.length > 15) b.trail.shift();\n";
  html += "      }\n";
  html += "      b.vy += g;\n";
  html += "      b.vx *= air;\n";
  html += "      b.vy *= air;\n";
  html += "      b.x += b.vx;\n";
  html += "      b.y += b.vy;\n";
  html += "      // Limitar velocidad máxima\n";
  html += "      b.vx = Math.max(Math.min(b.vx, 20), -20);\n";
  html += "      b.vy = Math.max(Math.min(b.vy, 20), -20);\n";
  html += "      // Efectos de energía\n";
  html += "      b.energy = Math.min(1, speed/10);\n";
  html += "      if(b.glow > 0) b.glow--;\n";
  html += "      // Suelo con efectos\n";
  html += "      if (b.y + b.r > canvas.height) { \n";
  html += "        b.y = canvas.height - b.r; \n";
  html += "        if(Math.abs(b.vy) > 2) {\n";
  html += "          for(let i=0;i<5;i++) particles.push(Particle(b.x+Math.random()*20-10, canvas.height, Math.random()*4-2, -Math.random()*3, b.color, 20));\n";
  html += "          screenShake = Math.min(5, Math.abs(b.vy));\n";
  html += "        }\n";
  html += "        b.vy = -b.vy * friction; b.vx *= friction; \n";
  html += "        if (Math.abs(b.vy) < 0.5) b.vy = 0; \n";
  html += "      }\n";
  html += "      // Techo\n";
  html += "      if (b.y - b.r < 0) { b.y = b.r; b.vy = -b.vy * friction; }\n";
  html += "      // Paredes\n";
  html += "      if (b.x - b.r < 0) { b.x = b.r; b.vx = -b.vx * friction; }\n";
  html += "      if (b.x + b.r > canvas.width) { b.x = canvas.width - b.r; b.vx = -b.vx * friction; }\n";
  html += "    }\n";
  html += "    // Actualizar trail\n";
  html += "    for(let t of b.trail) { t.alpha *= 0.95; t.size *= 0.98; }\n";
  html += "    b.trail = b.trail.filter(t => t.alpha > 0.1);\n";
  html += "  }\n";
  html += "  // Actualizar partículas\n";
  html += "  for(let p of particles) {\n";
  html += "    p.x += p.vx; p.y += p.vy;\n";
  html += "    p.vy += p.gravity;\n";
  html += "    p.vx *= 0.98; p.vy *= 0.98;\n";
  html += "    p.life--; p.alpha = p.life / p.maxLife;\n";
  html += "  }\n";
  html += "  particles = particles.filter(p => p.life > 0);\n";
  html += "  // Actualizar explosiones\n";
  html += "  for(let e of explosions) {\n";
  html += "    e.time++; e.intensity *= 0.9;\n";
  html += "    if(e.time < 10) {\n";
  html += "      for(let i=0;i<3;i++) {\n";
  html += "        let angle = Math.random()*Math.PI*2;\n";
  html += "        let speed = Math.random()*e.intensity*0.3;\n";
  html += "        particles.push(Particle(e.x, e.y, Math.cos(angle)*speed, Math.sin(angle)*speed, '#ff4444', 25));\n";
  html += "      }\n";
  html += "    }\n";
  html += "  }\n";
  html += "  explosions = explosions.filter(e => e.intensity > 0.5);\n";
  html += "  // Screen shake\n";
  html += "  if(screenShake > 0) screenShake *= 0.9;\n";
  html += "  for(let i=0;i<25;i++) resolveCollisions();\n";
  html += "}\n";
  html += "function draw() {\n";
  html += "  // Screen shake effect\n";
  html += "  let shakeX = screenShake > 0 ? (Math.random()-0.5)*screenShake : 0;\n";
  html += "  let shakeY = screenShake > 0 ? (Math.random()-0.5)*screenShake : 0;\n";
  html += "  ctx.save();\n";
  html += "  ctx.translate(shakeX, shakeY);\n";
  html += "  // Fondo con gradiente dinámico\n";
  html += "  let gradient = ctx.createRadialGradient(200, 130, 0, 200, 130, 300);\n";
  html += "  gradient.addColorStop(0, 'rgba(26,26,46,0.8)');\n";
  html += "  gradient.addColorStop(1, 'rgba(0,0,0,1)');\n";
  html += "  ctx.fillStyle = gradient;\n";
  html += "  ctx.fillRect(0, 0, canvas.width, canvas.height);\n";
  html += "  // Dibujar trails\n";
  html += "  for(let b of balls){\n";
  html += "    for(let i=0; i<b.trail.length; i++){\n";
  html += "      let t = b.trail[i];\n";
  html += "      ctx.globalAlpha = t.alpha * 0.6;\n";
  html += "      ctx.beginPath();\n";
  html += "      ctx.arc(t.x, t.y, t.size, 0, 2*Math.PI);\n";
  html += "      ctx.fillStyle = b.color;\n";
  html += "      ctx.fill();\n";
  html += "    }\n";
  html += "  }\n";
  html += "  ctx.globalAlpha = 1;\n";
  html += "  // Dibujar pelotas con efectos mejorados\n";
  html += "  for(let b of balls){\n";
  html += "    // Glow effect\n";
  html += "    if(b.glow > 0 || b.energy > 0.3) {\n";
  html += "      ctx.shadowColor = b.color;\n";
  html += "      ctx.shadowBlur = 20 + b.glow + b.energy*10;\n";
  html += "    } else {\n";
  html += "      ctx.shadowColor = '#000';\n";
  html += "      ctx.shadowBlur = 8;\n";
  html += "    }\n";
  html += "    // Gradiente radial para cada pelota\n";
  html += "    let ballGrad = ctx.createRadialGradient(b.x-b.r*0.3, b.y-b.r*0.3, 0, b.x, b.y, b.r);\n";
  html += "    ballGrad.addColorStop(0, glowColors[balls.indexOf(b)%glowColors.length]);\n";
  html += "    ballGrad.addColorStop(1, b.color);\n";
  html += "    ctx.beginPath();\n";
  html += "    ctx.arc(b.x, b.y, b.r, 0, 2*Math.PI);\n";
  html += "    ctx.fillStyle = ballGrad;\n";
  html += "    ctx.fill();\n";
  html += "    // Highlight\n";
  html += "    ctx.shadowBlur = 0;\n";
  html += "    ctx.beginPath();\n";
  html += "    ctx.arc(b.x-b.r*0.4, b.y-b.r*0.4, b.r*0.3, 0, 2*Math.PI);\n";
  html += "    ctx.fillStyle = 'rgba(255,255,255,0.4)';\n";
  html += "    ctx.fill();\n";
  html += "  }\n";
  html += "  // Dibujar partículas\n";
  html += "  ctx.shadowBlur = 5;\n";
  html += "  for(let p of particles) {\n";
  html += "    ctx.globalAlpha = p.alpha;\n";
  html += "    ctx.shadowColor = p.color;\n";
  html += "    ctx.beginPath();\n";
  html += "    ctx.arc(p.x, p.y, p.size, 0, 2*Math.PI);\n";
  html += "    ctx.fillStyle = p.color;\n";
  html += "    ctx.fill();\n";
  html += "  }\n";
  html += "  // Dibujar explosiones\n";
  html += "  for(let e of explosions) {\n";
  html += "    ctx.globalAlpha = e.intensity / e.maxIntensity;\n";
  html += "    ctx.shadowColor = '#ff4444';\n";
  html += "    ctx.shadowBlur = e.intensity;\n";
  html += "    ctx.beginPath();\n";
  html += "    ctx.arc(e.x, e.y, e.intensity*2, 0, 2*Math.PI);\n";
  html += "    ctx.fillStyle = 'rgba(255,68,68,0.3)';\n";
  html += "    ctx.fill();\n";
  html += "  }\n";
  html += "  ctx.globalAlpha = 1;\n";
  html += "  ctx.shadowBlur = 0;\n";
  html += "  ctx.restore();\n";
  html += "}\n";
  html += "let last=performance.now(),frames=0,fps=0;\n";
  html += "function loop() {\n";
  html += "  update(); draw();\n";
  html += "  frames++;\n";
  html += "  let now=performance.now();\n";
  html += "  if(now-last>1000){fps=frames;frames=0;last=now;}\n";
  html += "  document.getElementById('info').innerHTML='FPS: '+fps+' | Pelotas: '+balls.length+' | Partículas: '+particles.length+' | Explosiones: '+explosions.length+' | Shake: '+Math.round(screenShake);\n";
  html += "  requestAnimationFrame(loop);\n";
  html += "}\n";
  html += "loop();\n";
  html += "document.getElementById('grav').oninput=e=>{g=parseFloat(e.target.value);document.getElementById('gval').textContent=g.toFixed(2);}\n";
  html += "document.getElementById('fric').oninput=e=>{friction=parseFloat(e.target.value);document.getElementById('fval').textContent=friction.toFixed(2);}\n";
  html += "document.getElementById('air').oninput=e=>{air=parseFloat(e.target.value);document.getElementById('aval').textContent=air.toFixed(3);}\n";
  html += "let mouseHistory = [];\n";
  html += "canvas.addEventListener('mousedown',e=>{\n";
  html += "  let rect=canvas.getBoundingClientRect(),mx=e.clientX-rect.left,my=e.clientY-rect.top;\n";
  html += "  for(let b of balls){\n";
  html += "    if(distance(mx,my,b.x,b.y)<b.r){b.dragging=true;offsetX=mx-b.x;offsetY=my-b.y;}\n";
  html += "  }\n";
  html += "  mouseHistory = [{x:mx, y:my, t:performance.now()}];\n";
  html += "});\n";
  html += "canvas.addEventListener('mousemove',e=>{\n";
  html += "  let rect=canvas.getBoundingClientRect(),mx=e.clientX-rect.left,my=e.clientY-rect.top,now=performance.now();\n";
  html += "  for(let b of balls){\n";
  html += "    if(b.dragging){b.x=mx-offsetX;b.y=my-offsetY;}\n";
  html += "  }\n";
  html += "  mouseHistory.push({x:mx, y:my, t:now});\n";
  html += "  // Mantener solo los últimos 100 ms\n";
  html += "  while(mouseHistory.length>2 && now-mouseHistory[0].t>100) mouseHistory.shift();\n";
  html += "});\n";
  html += "canvas.addEventListener('mouseup',e=>{\n";
  html += "  let rect=canvas.getBoundingClientRect(),mx=e.clientX-rect.left,my=e.clientY-rect.top,now=performance.now();\n";
  html += "  for(let b of balls){\n";
  html += "    if(b.dragging){\n";
  html += "      // Buscar la posición del mouse 50 ms atrás\n";
  html += "      let prev = mouseHistory[0];\n";
  html += "      for(let i=mouseHistory.length-1;i>=0;i--){if(now-mouseHistory[i].t>50){prev=mouseHistory[i];break;}}\n";
  html += "      let dt = (now - prev.t)/1000;\n";
  html += "      if(dt>0){\n";
  html += "        b.vx = (mx-prev.x)/dt*0.05;\n";
  html += "        b.vy = (my-prev.y)/dt*0.05;\n";
  html += "      }\n";
  html += "      b.dragging=false;\n";
  html += "    }\n";
  html += "  }\n";
  html += "  mouseHistory = [];\n";
  html += "});\n";
  html += "canvas.addEventListener('dblclick',e=>{\n";
  html += "  let rect=canvas.getBoundingClientRect(),mx=e.clientX-rect.left,my=e.clientY-rect.top;\n";
  html += "  let newBall = Ball(mx,my,Math.random()*20+10,colors[Math.floor(Math.random()*colors.length)]);\n";
  html += "  newBall.vx = (Math.random()-0.5)*10;\n";
  html += "  newBall.vy = (Math.random()-0.5)*10;\n";
  html += "  balls.push(newBall);\n";
  html += "  // Efecto de creación\n";
  html += "  explosions.push(Explosion(mx, my, 15));\n";
  html += "  for(let i=0;i<10;i++) {\n";
  html += "    let angle = (i/10)*Math.PI*2;\n";
  html += "    particles.push(Particle(mx, my, Math.cos(angle)*5, Math.sin(angle)*5, newBall.color, 30));\n";
  html += "  }\n";
  html += "});\n";

  // --- Doom 3D avanzado ---
  html += "const doomCanvas=document.getElementById('doom');\n";
  html += "const dctx=doomCanvas.getContext('2d');\n";
  html += "const minimap=document.getElementById('minimap');\n";
  html += "const mctx=minimap.getContext('2d');\n";
  html += "const map=[\n";
  html += "[1,1,1,1,1,1,1,1,1,1,1,1],\n";
  html += "[1,4,4,0,0,0,0,0,0,5,5,1],\n";
  html += "[1,4,1,0,1,1,1,0,1,5,0,1],\n";
  html += "[1,0,1,0,0,0,1,0,1,1,0,1],\n";
  html += "[1,0,1,1,1,0,1,0,0,0,0,1],\n";
  html += "[1,0,0,0,1,0,1,1,1,1,0,1],\n";
  html += "[1,1,1,0,1,0,0,0,0,1,0,1],\n";
  html += "[1,0,0,0,1,1,1,1,0,1,0,1],\n";
  html += "[1,0,1,1,1,0,0,1,0,1,0,1],\n";
  html += "[1,0,0,0,0,0,1,1,0,5,5,1],\n";
  html += "[1,1,1,1,1,1,1,1,1,1,1,1]\n";
  html += "];\n";
  html += "let px=1.5,py=1.5,pa=0,keys={},hasKey=false,shots=[],enemies=[{x:7.5,y:7.5,hp:3},{x:9.5,y:2.5,hp:3}],health=100,maxHealth=100,ammo=30,maxAmmo=30,score=0,victory=false,doomShake=0,muzzleFlash=0,bloodEffects=[],lightSources=[{x:5.5,y:5.5,intensity:0.8,color:'#ffd600'},{x:9.5,y:8.5,intensity:0.6,color:'#00ff88'}];\n";
  html += "let players=[],playerId=Math.random().toString(36).substr(2,9),team=Math.random()>0.5?'CT':'T',money=800,weapon='pistol',reloading=false,reloadTime=0,roundTime=120,gameState='warmup',kills=0,deaths=0;\n";
  html += "let weapons={pistol:{damage:25,ammo:12,reload:2000,firerate:300,name:'Glock-18',price:0},rifle:{damage:35,ammo:30,reload:3000,firerate:100,name:'AK-47',price:2700},sniper:{damage:100,ammo:10,reload:4000,firerate:1500,name:'AWP',price:4750}};\n";
  html += "let mouseX=0,mouseY=0,mouseLocked=false,sensitivity=0.002;\n";
  html += "let bombSites=[{x:2.5,y:2.5,name:'A'},{x:9.5,y:9.5,name:'B'}],bomb={planted:false,x:0,y:0,timer:35,site:''},defusing=false,defuseTime=0;\n";
  html += "let objectives={CT:'Eliminar terroristas o desactivar bomba',T:'Plantar bomba o eliminar CT'};\n";
  html += "function drawDoom(){\n";
  html += "  // Screen shake para Doom\n";
  html += "  let doomShakeX = doomShake > 0 ? (Math.random()-0.5)*doomShake : 0;\n";
  html += "  let doomShakeY = doomShake > 0 ? (Math.random()-0.5)*doomShake : 0;\n";
  html += "  dctx.save();\n";
  html += "  dctx.translate(doomShakeX, doomShakeY);\n";
  html += "  dctx.clearRect(-20,-20,840,640);\n";
  html += "  // Suelo y techo con gradientes\n";
  html += "  let ceilingGrad = dctx.createLinearGradient(0,0,0,300);\n";
  html += "  ceilingGrad.addColorStop(0, '#1a1a2e');\n";
  html += "  ceilingGrad.addColorStop(1, '#333');\n";
  html += "  dctx.fillStyle = ceilingGrad;\n";
  html += "  dctx.fillRect(0,0,800,300);\n";
  html += "  let floorGrad = dctx.createLinearGradient(0,300,0,600);\n";
  html += "  floorGrad.addColorStop(0, '#222');\n";
  html += "  floorGrad.addColorStop(1, '#111');\n";
  html += "  dctx.fillStyle = floorGrad;\n";
  html += "  dctx.fillRect(0,300,800,300);\n";
  html += "  for(let x=0;x<800;x+=2){\n";
  html += "    let ra=pa-(Math.PI/3)+x*(Math.PI*2/3)/800;\n";
  html += "    let dist=0,hit=0,tx=px,ty=py,wallX=0;\n";
  html += "    while(!hit&&dist<15){\n";
  html += "      tx+=Math.cos(ra)*0.015;ty+=Math.sin(ra)*0.015;dist+=0.015;\n";
  html += "      let mx=Math.floor(tx),my=Math.floor(ty);\n";
  html += "      if(map[my]&&map[my][mx]){hit=map[my][mx];wallX=tx-mx;break;}\n";
  html += "    }\n";
  html += "    let h=400/(dist*Math.cos(ra-pa));\n";
  html += "    let fog=Math.max(0.1,1-Math.min(1,dist/12));\n";
  html += "    // Calcular iluminación dinámica\n";
  html += "    let lighting = 0.3;\n";
  html += "    for(let light of lightSources) {\n";
  html += "      let lightDist = Math.sqrt((tx-light.x)**2 + (ty-light.y)**2);\n";
  html += "      if(lightDist < 4) lighting += light.intensity * (1 - lightDist/4);\n";
  html += "    }\n";
  html += "    lighting = Math.min(1, lighting);\n";
  html += "    let shade = Math.floor(100 + 120*lighting - dist*15);\n";
  html += "    // Texturas procedurales\n";
  html += "    let textureOffset = Math.floor(wallX*32) % 4;\n";
  html += "    let textureMod = (textureOffset < 2) ? 1.1 : 0.9;\n";
  html += "    shade *= textureMod;\n";
  html += "    if(hit==1)dctx.fillStyle='rgba('+Math.floor(shade*0.8)+','+Math.floor(shade*0.8)+','+Math.floor(shade*1.2)+','+fog+')';\n";
  html += "    else if(hit==4)dctx.fillStyle='rgba('+Math.floor(255*lighting)+','+Math.floor(100*lighting)+','+Math.floor(100*lighting)+','+fog+')';\n";
  html += "    else if(hit==5)dctx.fillStyle='rgba('+Math.floor(100*lighting)+','+Math.floor(100*lighting)+','+Math.floor(255*lighting)+','+fog+')';\n";
  html += "    else dctx.fillStyle='rgba('+Math.floor(34*lighting)+','+Math.floor(34*lighting)+','+Math.floor(34*lighting)+','+fog+')';\n";
  html += "    dctx.fillRect(x,300-h/2,2,h);\n";
  html += "    // Efecto de scanlines\n";
  html += "    if(x%6==0) {\n";
  html += "      dctx.fillStyle='rgba(0,0,0,0.1)';\n";
  html += "      dctx.fillRect(x,300-h/2,2,h);\n";
  html += "    }\n";
  html += "  }\n";
  html += "  // Enemigos con efectos mejorados\n";
  html += "  for(let e of enemies){\n";
  html += "    let dx=e.x-px,dy=e.y-py,dist=Math.sqrt(dx*dx+dy*dy);\n";
  html += "    let angle=Math.atan2(dy,dx)-pa;\n";
  html += "    if(dist<12&&Math.abs(angle)<Math.PI/3){\n";
  html += "      let sx=400+Math.tan(angle)*800/2,sz=300/(dist*Math.cos(angle));\n";
  html += "      // Sombra del enemigo\n";
  html += "      dctx.fillStyle='rgba(0,0,0,0.3)';\n";
  html += "      dctx.beginPath();\n";
  html += "      dctx.ellipse(sx+3, 300+sz/2+3, sz/3, sz/8, 0, 0, 2*Math.PI);\n";
  html += "      dctx.fill();\n";
  html += "      // Cuerpo del enemigo con gradiente\n";
  html += "      let enemyGrad = dctx.createRadialGradient(sx-sz/6, 300-sz/6, 0, sx, 300, sz/2);\n";
  html += "      enemyGrad.addColorStop(0, '#ff6666');\n";
  html += "      enemyGrad.addColorStop(1, '#cc0000');\n";
  html += "      dctx.fillStyle = enemyGrad;\n";
  html += "      dctx.beginPath();\n";
  html += "      dctx.arc(sx,300,sz/2,0,2*Math.PI);\n";
  html += "      dctx.fill();\n";
  html += "      // Ojos brillantes\n";
  html += "      dctx.fillStyle='#ffff00';\n";
  html += "      dctx.beginPath();\n";
  html += "      dctx.arc(sx-sz/6,300-sz/8,sz/12,0,2*Math.PI);\n";
  html += "      dctx.arc(sx+sz/6,300-sz/8,sz/12,0,2*Math.PI);\n";
  html += "      dctx.fill();\n";
  html += "      // Efecto de daño\n";
  html += "      if(e.hp < 3) {\n";
  html += "        dctx.fillStyle='rgba(255,0,0,0.5)';\n";
  html += "        dctx.fillRect(sx-sz/2,300-sz/2,sz,sz);\n";
  html += "      }\n";
  html += "    }\n";
  html += "  }\n";
  html += "  // Renderizar otros jugadores\n";
  html += "  for(let p of players) {\n";
  html += "    let dx=p.x-px,dy=p.y-py,dist=Math.sqrt(dx*dx+dy*dy);\n";
  html += "    let angle=Math.atan2(dy,dx)-pa;\n";
  html += "    if(dist<15&&Math.abs(angle)<Math.PI/3){\n";
  html += "      let sx=400+Math.tan(angle)*800/2,sz=250/(dist*Math.cos(angle));\n";
  html += "      // Sombra del jugador\n";
  html += "      dctx.fillStyle='rgba(0,0,0,0.2)';\n";
  html += "      dctx.beginPath();\n";
  html += "      dctx.ellipse(sx+2, 300+sz/2+2, sz/4, sz/10, 0, 0, 2*Math.PI);\n";
  html += "      dctx.fill();\n";
  html += "      // Cuerpo del jugador\n";
  html += "      let playerColor = p.team=='CT' ? '#2196f3' : '#ff9800';\n";
  html += "      let playerGrad = dctx.createLinearGradient(sx, 300-sz/2, sx, 300+sz/2);\n";
  html += "      playerGrad.addColorStop(0, playerColor);\n";
  html += "      playerGrad.addColorStop(1, '#333');\n";
  html += "      dctx.fillStyle = playerGrad;\n";
  html += "      dctx.fillRect(sx-sz/8, 300-sz/2, sz/4, sz);\n";
  html += "      // Cabeza\n";
  html += "      dctx.fillStyle = '#ffdbac';\n";
  html += "      dctx.beginPath();\n";
  html += "      dctx.arc(sx, 300-sz/2-sz/8, sz/8, 0, 2*Math.PI);\n";
  html += "      dctx.fill();\n";
  html += "      // Nombre del jugador\n";
  html += "      dctx.fillStyle = '#fff';\n";
  html += "      dctx.font = '10px monospace';\n";
  html += "      dctx.fillText(p.name, sx-20, 300-sz/2-sz/4);\n";
  html += "    }\n";
  html += "  }\n";
  html += "  // Disparos mejorados\n";
  html += "  for(let s of shots){\n";
  html += "    let sx = 400+s.x*200, sy = 300+s.y*200;\n";
  html += "    // Trail del disparo\n";
  html += "    dctx.strokeStyle='rgba(255,255,0,0.8)';\n";
  html += "    dctx.lineWidth=3;\n";
  html += "    dctx.beginPath();\n";
  html += "    dctx.moveTo(sx-s.vx*5, sy-s.vy*5);\n";
  html += "    dctx.lineTo(sx, sy);\n";
  html += "    dctx.stroke();\n";
  html += "    // Proyectil\n";
  html += "    dctx.fillStyle='#ffff00';\n";
  html += "    dctx.shadowColor='#ffff00';\n";
  html += "    dctx.shadowBlur=10;\n";
  html += "    dctx.fillRect(sx-2,sy-2,4,4);\n";
  html += "    dctx.shadowBlur=0;\n";
  html += "  }\n";
  html += "  // Efectos de sangre\n";
  html += "  for(let b of bloodEffects) {\n";
  html += "    dctx.globalAlpha = b.alpha;\n";
  html += "    dctx.fillStyle = '#cc0000';\n";
  html += "    dctx.fillRect(b.x-1, b.y-1, 2, 2);\n";
  html += "  }\n";
  html += "  dctx.globalAlpha = 1;\n";
  html += "  // Muzzle flash mejorado\n";
  html += "  if(muzzleFlash>0){\n";
  html += "    let flashIntensity = muzzleFlash/8;\n";
  html += "    // Flash central\n";
  html += "    dctx.fillStyle='rgba(255,255,100,'+flashIntensity+')';\n";
  html += "    dctx.fillRect(0,0,800,600);\n";
  html += "    // Efecto de arma\n";
  html += "    dctx.fillStyle='rgba(255,200,0,'+flashIntensity*2+')';\n";
  html += "    dctx.fillRect(360,560,80,40);\n";
  html += "    muzzleFlash--;\n";
  html += "  }\n";
  html += "  // HUD estilo CS:GO\n";
  html += "  dctx.font='16px monospace';\n";
  html += "  // Fondo del HUD\n";
  html += "  dctx.fillStyle='rgba(0,0,0,0.8)';\n";
  html += "  dctx.fillRect(10,10,780,35);\n";
  html += "  dctx.fillRect(10,550,780,40);\n";
  html += "  // Barra de salud\n";
  html += "  dctx.fillStyle='rgba(255,0,0,0.3)';\n";
  html += "  dctx.fillRect(20,560,200,8);\n";
  html += "  dctx.fillStyle='#00ff00';\n";
  html += "  dctx.fillRect(20,560,(health/maxHealth)*200,8);\n";
  html += "  // Barra de munición\n";
  html += "  dctx.fillStyle='rgba(255,255,0,0.3)';\n";
  html += "  dctx.fillRect(240,560,150,8);\n";
  html += "  dctx.fillStyle='#ffff00';\n";
  html += "  dctx.fillRect(240,560,(ammo/maxAmmo)*150,8);\n";
  html += "  // Información del jugador\n";
  html += "  dctx.fillStyle='#fff';\n";
  html += "  dctx.fillText('Salud: '+Math.floor(health), 20, 30);\n";
  html += "  dctx.fillText('Munición: '+ammo+'/'+(reloading?'Recargando...':maxAmmo), 150, 30);\n";
  html += "  dctx.fillText('Arma: '+weapons[weapon].name, 350, 30);\n";
  html += "  dctx.fillText('Dinero: $'+money, 550, 30);\n";
  html += "  dctx.fillText('K/D: '+kills+'/'+deaths, 680, 30);\n";
  html += "  // Información del juego\n";
  html += "  dctx.fillStyle='#ffd600';\n";
  html += "  dctx.fillText('Equipo: '+team+' | Jugadores: '+(players.length+1), 20, 580);\n";
  html += "  dctx.fillText('Tiempo: '+Math.floor(roundTime)+'s', 300, 580);\n";
  html += "  dctx.fillText('Estado: '+gameState.toUpperCase(), 450, 580);\n";
  html += "  // Información de bomba\n";
  html += "  if(bomb.planted) {\n";
  html += "    dctx.fillStyle = bomb.timer < 10 ? '#ff0000' : '#ff9800';\n";
  html += "    dctx.fillText('BOMBA EN SITIO '+bomb.site+': '+Math.floor(bomb.timer)+'s', 600, 580);\n";
  html += "  }\n";
  html += "  if(defusing) {\n";
  html += "    dctx.fillStyle = '#00ff00';\n";
  html += "    dctx.fillText('DESACTIVANDO: '+Math.floor(defuseTime)+'s', 600, 560);\n";
  html += "  }\n";
  html += "  // Objetivo del equipo\n";
  html += "  dctx.fillStyle = '#fff';\n";
  html += "  dctx.fillText('Objetivo: '+objectives[team], 20, 560);\n";
  html += "  // Controles\n";
  html += "  dctx.fillStyle = '#aaa';\n";
  html += "  dctx.fillText('WASD: Mover | Mouse: Mirar | Click: Disparar | R: Recargar | B: Comprar | E: Plantar/Desactivar', 20, 575);\n";
  html += "  // Crosshair estilo CS:GO\n";
  html += "  dctx.strokeStyle = reloading ? '#ff0000' : '#00ff00';\n";
  html += "  dctx.lineWidth = 2;\n";
  html += "  dctx.beginPath();\n";
  html += "  dctx.moveTo(385, 300); dctx.lineTo(415, 300);\n";
  html += "  dctx.moveTo(400, 285); dctx.lineTo(400, 315);\n";
  html += "  dctx.stroke();\n";
  html += "  // Círculo del crosshair\n";
  html += "  dctx.strokeStyle = 'rgba(255,255,255,0.3)';\n";
  html += "  dctx.lineWidth = 1;\n";
  html += "  dctx.beginPath();\n";
  html += "  dctx.arc(400, 300, 20, 0, 2*Math.PI);\n";
  html += "  dctx.stroke();\n";
  html += "  dctx.restore();\n";
  html += "}\n";
  html += "function updateDoom(){\n";
  html += "  if(victory)return;\n";
  html += "  // Actualizar recarga\n";
  html += "  if(reloading) {\n";
  html += "    reloadTime -= 16;\n";
  html += "    if(reloadTime <= 0) {\n";
  html += "      reloading = false;\n";
  html += "      ammo = weapons[weapon].ammo;\n";
  html += "      maxAmmo = weapons[weapon].ammo;\n";
  html += "    }\n";
  html += "  }\n";
  html += "  // Recarga automática\n";
  html += "  if(ammo <= 0 && !reloading) {\n";
  html += "    reloading = true;\n";
  html += "    reloadTime = weapons[weapon].reload;\n";
  html += "  }\n";
  html += "  // Movimiento con WASD\n";
  html += "  let spd=0.08;\n";
  html += "  if(keys['w']||keys['W']){let nx=px+Math.cos(pa)*spd,ny=py+Math.sin(pa)*spd;if(!map[Math.floor(ny)][Math.floor(nx)]){px=nx;py=ny;}}\n";
  html += "  if(keys['s']||keys['S']){let nx=px-Math.cos(pa)*spd,ny=py-Math.sin(pa)*spd;if(!map[Math.floor(ny)][Math.floor(nx)]){px=nx;py=ny;}}\n";
  html += "  if(keys['a']||keys['A']){let nx=px+Math.cos(pa-Math.PI/2)*spd,ny=py+Math.sin(pa-Math.PI/2)*spd;if(!map[Math.floor(ny)][Math.floor(nx)]){px=nx;py=ny;}}\n";
  html += "  if(keys['d']||keys['D']){let nx=px+Math.cos(pa+Math.PI/2)*spd,ny=py+Math.sin(pa+Math.PI/2)*spd;if(!map[Math.floor(ny)][Math.floor(nx)]){px=nx;py=ny;}}\n";
  html += "  // Mecánicas de bomba\n";
  html += "  let currentTile = map[Math.floor(py)][Math.floor(px)];\n";
  html += "  if(team=='T' && !bomb.planted && (currentTile==4 || currentTile==5) && keys['e']) {\n";
  html += "    bomb.planted = true;\n";
  html += "    bomb.x = px; bomb.y = py;\n";
  html += "    bomb.site = currentTile==4 ? 'A' : 'B';\n";
  html += "    bomb.timer = 35;\n";
  html += "    money += 300;\n";
  html += "  }\n";
  html += "  if(team=='CT' && bomb.planted && Math.abs(px-bomb.x)<0.5 && Math.abs(py-bomb.y)<0.5) {\n";
  html += "    if(keys['e'] && !defusing) {\n";
  html += "      defusing = true;\n";
  html += "      defuseTime = 10;\n";
  html += "    }\n";
  html += "    if(defusing) {\n";
  html += "      defuseTime -= 0.016;\n";
  html += "      if(defuseTime <= 0) {\n";
  html += "        bomb.planted = false;\n";
  html += "        defusing = false;\n";
  html += "        money += 500;\n";
  html += "        victory = true;\n";
  html += "      }\n";
  html += "    }\n";
  html += "  }\n";
  html += "  // Timer de bomba\n";
  html += "  if(bomb.planted) {\n";
  html += "    bomb.timer -= 0.016;\n";
  html += "    if(bomb.timer <= 0) {\n";
  html += "      // Explosión\n";
  html += "      victory = true;\n";
  html += "      gameState = 'T wins';\n";
  html += "    }\n";
  html += "  }\n";
  html += "  // Disparos mejorados\n";
  html += "  for(let s of shots){\n";
  html += "    s.vx = s.vx || Math.cos(pa)*0.25;\n";
  html += "    s.vy = s.vy || Math.sin(pa)*0.25;\n";
  html += "    s.x += s.vx; s.y += s.vy;\n";
  html += "    // Colisión con paredes\n";
  html += "    let sx = px + s.x, sy = py + s.y;\n";
  html += "    if(map[Math.floor(sy)] && map[Math.floor(sy)][Math.floor(sx)]) {\n";
  html += "      // Efecto de impacto en pared\n";
  html += "      for(let i=0;i<5;i++) {\n";
  html += "        bloodEffects.push({x:Math.random()*400, y:Math.random()*260, alpha:0.8});\n";
  html += "      }\n";
  html += "      s.x = 99; // Marcar para eliminación\n";
  html += "    }\n";
  html += "  }\n";
  html += "  shots=shots.filter(s=>s.x>-3&&s.x<3&&s.y>-3&&s.y<3);\n";
  html += "  // Enemigos\n";
  html += "  for(let e of enemies){\n";
  html += "    // Movimiento simple hacia el jugador\n";
  html += "    let dx=px-e.x,dy=py-e.y,dist=Math.sqrt(dx*dx+dy*dy);\n";
  html += "    if(dist>0.5){e.x+=dx/dist*0.01;e.y+=dy/dist*0.01;}\n";
  html += "    // Colisión con disparo mejorada\n";
  html += "    for(let s of shots){\n";
  html += "      let sx=px+s.x,sy=py+s.y;\n";
  html += "      if(Math.sqrt((e.x-sx)**2+(e.y-sy)**2)<0.4){\n";
  html += "        e.hp--;\n";
  html += "        s.x=99;\n";
  html += "        doomShake = 8;\n";
  html += "        // Efectos de sangre\n";
  html += "        for(let i=0;i<8;i++) {\n";
  html += "          bloodEffects.push({x:Math.random()*400, y:Math.random()*260, alpha:1});\n";
  html += "        }\n";
  html += "        score += 5;\n";
  html += "      }\n";
  html += "    }\n";
  html += "    // Daño al jugador\n";
  html += "    if(dist<0.5){\n";
  html += "      health--;\n";
  html += "      doomShake = 15;\n";
  html += "      e.x=Math.random()*2+7; e.y=Math.random()*2+7;\n";
  html += "      // Efecto de daño en pantalla\n";
  html += "      for(let i=0;i<10;i++) {\n";
  html += "        bloodEffects.push({x:Math.random()*400, y:Math.random()*260, alpha:0.6});\n";
  html += "      }\n";
  html += "    }\n";
  html += "  }\n";
  html += "  enemies=enemies.filter(e=>e.hp>0);\n";
  html += "  score=10*(2-enemies.length);\n";
  html += "  // Actualizar efectos de sangre\n";
  html += "  for(let b of bloodEffects) {\n";
  html += "    b.alpha *= 0.95;\n";
  html += "    b.y += 0.5; // Goteo\n";
  html += "  }\n";
  html += "  bloodEffects = bloodEffects.filter(b => b.alpha > 0.1);\n";
  html += "  // Screen shake\n";
  html += "  if(doomShake > 0) doomShake *= 0.9;\n";
  html += "  // Recolección de salud y munición con efectos\n";
  html += "  if(Math.abs(px-5.5)<0.4&&Math.abs(py-5.5)<0.4&&health<5){\n";
  html += "    health=5;\n";
  html += "    for(let i=0;i<5;i++) {\n";
  html += "      bloodEffects.push({x:Math.random()*400, y:Math.random()*260, alpha:0.5});\n";
  html += "    }\n";
  html += "  }\n";
  html += "  if(Math.abs(px-9.5)<0.4&&Math.abs(py-8.5)<0.4&&ammo<10){\n";
  html += "    ammo=10;\n";
  html += "    for(let i=0;i<5;i++) {\n";
  html += "      bloodEffects.push({x:Math.random()*400, y:Math.random()*260, alpha:0.3});\n";
  html += "    }\n";
  html += "  }\n";
  html += "}\n";
  html += "let flash=0;\n";
  html += "// Controles de mouse y teclado\n";
  html += "document.addEventListener('keydown',e=>{\n";
  html += "  keys[e.key]=1;\n";
  html += "  if(e.key.toLowerCase()=='r'&&!reloading&&ammo<maxAmmo){\n";
  html += "    reloading=true;reloadTime=weapons[weapon].reload;\n";
  html += "  }\n";
  html += "  if(e.key=='b'||e.key=='B'){\n";
  html += "    // Menú de compra simple\n";
  html += "    if(money>=2700&&weapon!='rifle'){weapon='rifle';money-=2700;}\n";
  html += "    else if(money>=4750&&weapon!='sniper'){weapon='sniper';money-=4750;}\n";
  html += "  }\n";
  html += "});\n";
  html += "document.addEventListener('keyup',e=>{keys[e.key]=0;});\n";
  html += "// Mouse controls\n";
  html += "let lastShot = 0;\n";
  html += "doomCanvas.addEventListener('click',()=>{\n";
  html += "  if(!mouseLocked) {\n";
  html += "    doomCanvas.requestPointerLock();\n";
  html += "  } else if(ammo>0&&!reloading&&Date.now()-lastShot>weapons[weapon].firerate) {\n";
  html += "    shots.push({x:0,y:0,vx:Math.cos(pa)*0.3,vy:Math.sin(pa)*0.3});\n";
  html += "    ammo--;muzzleFlash=10;doomShake=5;lastShot=Date.now();\n";
  html += "  }\n";
  html += "});\n";
  html += "document.addEventListener('pointerlockchange',()=>{\n";
  html += "  mouseLocked = document.pointerLockElement === doomCanvas;\n";
  html += "  document.getElementById('lockMouse').style.display = mouseLocked ? 'none' : 'block';\n";
  html += "});\n";
  html += "document.addEventListener('mousemove',e=>{\n";
  html += "  if(mouseLocked) {\n";
  html += "    pa += e.movementX * sensitivity;\n";
  html += "    // Limitar rotación vertical (simulada)\n";
  html += "    mouseY = Math.max(-50, Math.min(50, mouseY + e.movementY * sensitivity * 100));\n";
  html += "  }\n";
  html += "});\n";
  html += "// Botón de bloqueo de mouse\n";
  html += "document.getElementById('lockMouse').addEventListener('click', () => {\n";
  html += "  doomCanvas.requestPointerLock();\n";
  html += "});\n";
  html += "// Simulación multijugador\n";
  html += "function updateMultiplayer() {\n";
  html += "  // Simular otros jugadores\n";
  html += "  if(players.length < 7) {\n";
  html += "    players.push({\n";
  html += "      id: 'bot_' + Math.random().toString(36).substr(2,5),\n";
  html += "      x: Math.random()*10+1,\n";
  html += "      y: Math.random()*10+1,\n";
  html += "      angle: Math.random()*Math.PI*2,\n";
  html += "      team: Math.random() > 0.5 ? 'CT' : 'T',\n";
  html += "      health: 100,\n";
  html += "      name: 'Bot' + Math.floor(Math.random()*100)\n";
  html += "    });\n";
  html += "  }\n";
  html += "  // Mover bots\n";
  html += "  for(let p of players) {\n";
  html += "    p.angle += (Math.random()-0.5)*0.1;\n";
  html += "    let nx = p.x + Math.cos(p.angle)*0.02;\n";
  html += "    let ny = p.y + Math.sin(p.angle)*0.02;\n";
  html += "    if(!map[Math.floor(ny)] || !map[Math.floor(ny)][Math.floor(nx)]) {\n";
  html += "      p.x = nx; p.y = ny;\n";
  html += "    }\n";
  html += "  }\n";
  html += "  // Actualizar tiempo de ronda\n";
  html += "  roundTime -= 0.016;\n";
  html += "  if(roundTime <= 0) {\n";
  html += "    roundTime = 120;\n";
  html += "    money += 1400;\n";
  html += "    gameState = 'nueva ronda';\n";
  html += "  }\n";
  html += "}\n";
  html += "function doomLoop(){updateDoom();updateMultiplayer();drawDoom();drawMinimap();requestAnimationFrame(doomLoop);}doomLoop();\n";
  html += "function drawMinimap(){\n";
  html += "  mctx.clearRect(0,0,120,110);\n";
  html += "  for(let y=0;y<map.length;y++)for(let x=0;x<map[0].length;x++){\n";
  html += "    if(map[y][x]==1)mctx.fillStyle='#fff';\n";
  html += "    else if(map[y][x]==4)mctx.fillStyle='#ff4444';\n";
  html += "    else if(map[y][x]==5)mctx.fillStyle='#4444ff';\n";
  html += "    else mctx.fillStyle='#222';\n";
  html += "    mctx.fillRect(x*10,y*10,10,10);\n";
  html += "  }\n";
  html += "  // Etiquetas de sitios\n";
  html += "  mctx.fillStyle='#fff';\n";
  html += "  mctx.font='8px monospace';\n";
  html += "  mctx.fillText('A', 25, 25);\n";
  html += "  mctx.fillText('B', 95, 95);\n";
  html += "  // Bomba plantada\n";
  html += "  if(bomb.planted) {\n";
  html += "    mctx.fillStyle = bomb.timer < 10 ? '#ff0000' : '#ff9800';\n";
  html += "    mctx.beginPath();\n";
  html += "    mctx.arc(bomb.x*10, bomb.y*10, 6, 0, 2*Math.PI);\n";
  html += "    mctx.fill();\n";
  html += "  }\n";
  html += "  // Jugador principal\n";
  html += "  mctx.fillStyle = team=='CT' ? '#2196f3' : '#ff9800';\n";
  html += "  mctx.beginPath();mctx.arc(px*10,py*10,5,0,2*Math.PI);mctx.fill();\n";
  html += "  // Dirección del jugador\n";
  html += "  mctx.strokeStyle = mctx.fillStyle;\n";
  html += "  mctx.lineWidth = 2;\n";
  html += "  mctx.beginPath();\n";
  html += "  mctx.moveTo(px*10, py*10);\n";
  html += "  mctx.lineTo(px*10 + Math.cos(pa)*8, py*10 + Math.sin(pa)*8);\n";
  html += "  mctx.stroke();\n";
  html += "  // Otros jugadores\n";
  html += "  for(let p of players) {\n";
  html += "    mctx.fillStyle = p.team=='CT' ? '#2196f3' : '#ff9800';\n";
  html += "    mctx.beginPath();\n";
  html += "    mctx.arc(p.x*10, p.y*10, 3, 0, 2*Math.PI);\n";
  html += "    mctx.fill();\n";
  html += "  }\n";
  html += "  // Enemigos\n";
  html += "  mctx.fillStyle='#f44336';\n";
  html += "  for(let e of enemies){\n";
  html += "    mctx.beginPath();\n";
  html += "    mctx.arc(e.x*10,e.y*10,4,0,2*Math.PI);\n";
  html += "    mctx.fill();\n";
  html += "  }\n";
  html += "}\n";
  html += "</script>";
  html += "<footer style='text-align:center;margin-top:30px;color:#888;'>ESP8266: Simulación física y Doom 3D en tu navegador</footer>";
  html += "</body></html>";
  server.send(200, "text/html", html);
}

void setup() {
  Serial.begin(115200);
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nWiFi conectado!");
  Serial.print("IP: ");
  Serial.println(WiFi.localIP());
  server.on("/", handleRoot);
  server.begin();
  Serial.println("Servidor web iniciado.");
}

void loop() {
  server.handleClient();
}
