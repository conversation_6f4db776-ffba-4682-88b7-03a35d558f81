; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp01_1m]
platform = espressif8266
board = esp01_1m
framework = arduino
monitor_speed = 115200
lib_deps =
    ESP8266WiFi
    ESP8266WebServer
    ESP8266HTTPClient
    bblanchon/Arduino<PERSON><PERSON>@^6.21.3

; Para usar con NodeMCU o Wemos D1 Mini (recomendado)
[env:nodemcuv2]
platform = espressif8266
board = nodemcuv2
framework = arduino
monitor_speed = 115200
lib_deps =
    ESP8266WiFi
    ESP8266WebServer
    ESP8266HTTPClient
    bblanchon/Arduino<PERSON>son@^6.21.3
