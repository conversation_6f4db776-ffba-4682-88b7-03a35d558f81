# ESP8266 WiFi Repeater con Limitador de Datos

Este proyecto convierte un ESP8266 en un repetidor WiFi con funcionalidad de limitador de datos por cliente.

## 🚀 Características

- **Repetidor WiFi**: Se conecta a una red WiFi existente y crea un punto de acceso
- **Limitador de datos**: Controla el uso de datos por cliente (configurable en MB)
- **Monitoreo en tiempo real**: Interfaz web para ver estadísticas de uso
- **Notificaciones**: Alerta cuando un cliente excede el límite
- **Desconexión automática**: Desconecta clientes que superan el límite
- **API REST**: Endpoint JSON para integración con otros sistemas

## 📋 Requisitos

### Hardware
- ESP8266 (NodeMCU, Wemos D1 Mini, ESP-01, etc.)
- Conexión a internet vía WiFi

### Software
- PlatformIO IDE
- Framework Arduino para ESP8266

## ⚙️ Configuración

### 1. Configurar credenciales WiFi

Edita las siguientes líneas en `src/main.cpp`:

```cpp
const char* ssid_origen = "TU_WIFI_ORIGEN";        // WiFi al que se conectará el ESP
const char* password_origen = "TU_PASSWORD_ORIGEN"; // Password del WiFi origen

const char* ap_ssid = "ESP8266_Hotspot";           // Nombre del hotspot
const char* ap_password = "12345678";              // Password del hotspot
```

### 2. Configurar límites

```cpp
const unsigned long LIMITE_MB = 100;               // Límite en MB por cliente
const int MAX_CLIENTES = 4;                        // Máximo número de clientes
```

### 3. Compilar y subir

```bash
pio run --target upload
```

## 🌐 Uso

### Conexión
1. El ESP8266 se conectará automáticamente al WiFi configurado
2. Creará un punto de acceso con el nombre configurado
3. Los dispositivos pueden conectarse al hotspot

### Interfaz Web
Accede a `http://***********` desde cualquier dispositivo conectado al hotspot para ver:

- Estado del sistema
- Clientes conectados
- Uso de datos por cliente
- Estadísticas en tiempo real

### Endpoints disponibles

- `GET /` - Página principal con interfaz gráfica
- `GET /stats` - Estadísticas en formato JSON
- `GET /limite` - Página de notificación de límite excedido
- `GET /reset` - Reiniciar estadísticas de todos los clientes

## 📊 Monitoreo

### Interfaz Web
La página principal muestra:
- Estado de conexión WiFi origen
- Estado del punto de acceso
- Lista de clientes conectados
- Barras de progreso del uso de datos
- Alertas cuando se excede el límite

### API JSON
El endpoint `/stats` devuelve:

```json
{
  "sistema": {
    "wifi_origen_conectado": true,
    "ap_activo": true,
    "ip_origen": "*************",
    "ip_ap": "***********",
    "limite_mb": 100,
    "max_clientes": 4,
    "uptime_segundos": 3600
  },
  "clientes": [
    {
      "ip": "***********",
      "bytes_usados": 52428800,
      "mb_usados": 50.00,
      "porcentaje_limite": 50.0,
      "limite_excedido": false,
      "notificado": false,
      "ultima_actividad": 123456
    }
  ]
}
```

## ⚠️ Limitaciones Técnicas

### Medición de Datos
- **Simulación**: El código actual simula el uso de datos para demostración
- **Implementación real**: Requiere integración con el stack de red del ESP8266
- **Precisión**: La medición exacta de datos requiere modificaciones a nivel de firmware

### Desconexión de Clientes
- **Limitada**: ESP8266 tiene capacidades limitadas para desconectar clientes forzosamente
- **Workaround**: El código resetea la información del cliente y puede requerir reinicio del AP

## 🔧 Personalización

### Cambiar intervalo de verificación
```cpp
const unsigned long INTERVALO_VERIFICACION = 5000; // 5 segundos
```

### Modificar estilos de la interfaz web
Edita la función `generarPaginaEstado()` para personalizar el CSS.

### Agregar notificaciones
Modifica la función `notificarLimiteExcedido()` para enviar:
- Emails
- Notificaciones push
- Mensajes a servicios externos

## 🐛 Solución de Problemas

### No se conecta al WiFi origen
- Verificar credenciales
- Comprobar alcance de señal
- Revisar configuración de seguridad de la red

### Clientes no pueden conectarse al hotspot
- Verificar que el password tenga al menos 8 caracteres
- Comprobar que no se haya alcanzado el límite de clientes
- Reiniciar el ESP8266

### Interfaz web no carga
- Verificar que estés conectado al hotspot del ESP8266
- Acceder a `http://***********`
- Comprobar que el servidor web esté iniciado (revisar Serial Monitor)

## 📝 Notas de Desarrollo

Este es un proyecto de demostración que muestra los conceptos básicos. Para uso en producción, considera:

1. **Medición real de datos**: Implementar hooks en el stack de red
2. **Persistencia**: Guardar estadísticas en EEPROM o SPIFFS
3. **Seguridad**: Agregar autenticación para funciones administrativas
4. **Escalabilidad**: Optimizar para más clientes simultáneos
5. **Logging**: Implementar sistema de logs más robusto

## 📄 Licencia

Este proyecto es de código abierto y está disponible bajo la licencia MIT.
