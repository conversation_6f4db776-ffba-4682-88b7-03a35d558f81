#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>

// ===== CONFIGURACIÓN WiFi - CAMBIAR ESTOS VALORES =====
const char* ssid = "Redmi Note 12 5G";        // ⚠️ Cambiar por tu WiFi
const char* password = "123456789";            // ⚠️ Cambiar por tu password

// ===== CONFIGURACIÓN DE PINES LEDs =====
const int LED_ROJO = 2;      // GPIO2 (LED integrado en la mayoría de ESP8266)
const int LED_VERDE = 0;     // GPIO0
const int LED_AZUL = 4;      // GPIO4
const int LED_AMARILLO = 5;  // GPIO5

// Si solo tienes el LED integrado, usa solo LED_ROJO
// const int LED_INTEGRADO = 2;  // LED integrado (NodeMCU/Wemos D1 Mini)

// Servidor web
ESP8266WebServer server(80);

// Estados de los LEDs
bool estadoLedRojo = false;
bool estadoLedVerde = false;
bool estadoLedAzul = false;
bool estadoLedAmarillo = false;

// Variables para efectos
bool modoArcoiris = false;
bool modoParpadeo = false;
unsigned long ultimoParpadeo = 0;
int colorActual = 0;

// Declaraciones de funciones
void configurarPines();
void conectarWiFi();
void configurarServidor();
void controlarLED(int pin, bool estado);
void efectoArcoiris();
void efectoParpadeo();
String generarPaginaWeb();

void setup() {
  Serial.begin(115200);
  delay(1000);

  Serial.println("\n🔥 ESP8266 Controlador de LEDs por WiFi 🔥");
  Serial.println("==========================================");

  // Configurar pines de los LEDs
  configurarPines();

  // Conectar a WiFi
  conectarWiFi();

  // Configurar servidor web
  configurarServidor();

  Serial.println("✅ Sistema iniciado correctamente!");
  Serial.print("🌐 Dirección IP: ");
  Serial.println(WiFi.localIP());
  Serial.println("📱 Abre esta IP en tu navegador para controlar los LEDs");
  Serial.println("==========================================");
}

void loop() {
  // Manejar servidor web
  server.handleClient();

  // Verificar conexión WiFi
  if(WiFi.status() != WL_CONNECTED) {
    Serial.println("❌ WiFi desconectado, reintentando...");
    conectarWiFi();
  }

  // Ejecutar efectos especiales
  if(modoArcoiris) {
    efectoArcoiris();
  }

  if(modoParpadeo) {
    efectoParpadeo();
  }

  delay(50);
}

void configurarPines() {
  // Configurar pines como salida
  pinMode(LED_ROJO, OUTPUT);
  pinMode(LED_VERDE, OUTPUT);
  pinMode(LED_AZUL, OUTPUT);
  pinMode(LED_AMARILLO, OUTPUT);

  // Apagar todos los LEDs inicialmente
  digitalWrite(LED_ROJO, LOW);
  digitalWrite(LED_VERDE, LOW);
  digitalWrite(LED_AZUL, LOW);
  digitalWrite(LED_AMARILLO, LOW);

  Serial.println("🔧 Pines configurados:");
  Serial.println("   LED Rojo: GPIO" + String(LED_ROJO));
  Serial.println("   LED Verde: GPIO" + String(LED_VERDE));
  Serial.println("   LED Azul: GPIO" + String(LED_AZUL));
  Serial.println("   LED Amarillo: GPIO" + String(LED_AMARILLO));
}

void conectarWiFi() {
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);

  Serial.print("🔄 Conectando a WiFi: ");
  Serial.println(ssid);

  int intentos = 0;
  while(WiFi.status() != WL_CONNECTED && intentos < 20) {
    delay(500);
    Serial.print(".");
    intentos++;
  }

  if(WiFi.status() == WL_CONNECTED) {
    Serial.println("\n✅ WiFi conectado!");
    Serial.print("📡 IP asignada: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("\n❌ Error: No se pudo conectar al WiFi");
    Serial.println("⚠️  Verifica SSID y password en el código");
  }
}

void configurarServidor() {
  // Página principal
  server.on("/", [](){
    server.send(200, "text/html", generarPaginaWeb());
  });

  // Control individual de LEDs
  server.on("/led/rojo/on", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_ROJO, true);
    estadoLedRojo = true;
    request->send(200, "text/plain", "LED Rojo ENCENDIDO");
    Serial.println("🔴 LED Rojo ENCENDIDO");
  });

  server.on("/led/rojo/off", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_ROJO, false);
    estadoLedRojo = false;
    request->send(200, "text/plain", "LED Rojo APAGADO");
    Serial.println("⚫ LED Rojo APAGADO");
  });

  server.on("/led/verde/on", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_VERDE, true);
    estadoLedVerde = true;
    request->send(200, "text/plain", "LED Verde ENCENDIDO");
    Serial.println("🟢 LED Verde ENCENDIDO");
  });

  server.on("/led/verde/off", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_VERDE, false);
    estadoLedVerde = false;
    request->send(200, "text/plain", "LED Verde APAGADO");
    Serial.println("⚫ LED Verde APAGADO");
  });

  server.on("/led/azul/on", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_AZUL, true);
    estadoLedAzul = true;
    request->send(200, "text/plain", "LED Azul ENCENDIDO");
    Serial.println("🔵 LED Azul ENCENDIDO");
  });

  server.on("/led/azul/off", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_AZUL, false);
    estadoLedAzul = false;
    request->send(200, "text/plain", "LED Azul APAGADO");
    Serial.println("⚫ LED Azul APAGADO");
  });

  server.on("/led/amarillo/on", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_AMARILLO, true);
    estadoLedAmarillo = true;
    request->send(200, "text/plain", "LED Amarillo ENCENDIDO");
    Serial.println("🟡 LED Amarillo ENCENDIDO");
  });

  server.on("/led/amarillo/off", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_AMARILLO, false);
    estadoLedAmarillo = false;
    request->send(200, "text/plain", "LED Amarillo APAGADO");
    Serial.println("⚫ LED Amarillo APAGADO");
  });

  // Controles especiales
  server.on("/todos/on", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_ROJO, true);
    controlarLED(LED_VERDE, true);
    controlarLED(LED_AZUL, true);
    controlarLED(LED_AMARILLO, true);
    estadoLedRojo = estadoLedVerde = estadoLedAzul = estadoLedAmarillo = true;
    modoArcoiris = modoParpadeo = false;
    request->send(200, "text/plain", "Todos los LEDs ENCENDIDOS");
    Serial.println("🌟 Todos los LEDs ENCENDIDOS");
  });

  server.on("/todos/off", HTTP_GET, [](AsyncWebServerRequest *request){
    controlarLED(LED_ROJO, false);
    controlarLED(LED_VERDE, false);
    controlarLED(LED_AZUL, false);
    controlarLED(LED_AMARILLO, false);
    estadoLedRojo = estadoLedVerde = estadoLedAzul = estadoLedAmarillo = false;
    modoArcoiris = modoParpadeo = false;
    request->send(200, "text/plain", "Todos los LEDs APAGADOS");
    Serial.println("⚫ Todos los LEDs APAGADOS");
  });

  // Efectos especiales
  server.on("/efecto/arcoiris", HTTP_GET, [](AsyncWebServerRequest *request){
    modoArcoiris = !modoArcoiris;
    modoParpadeo = false;
    if(modoArcoiris) {
      request->send(200, "text/plain", "Efecto Arcoíris ACTIVADO");
      Serial.println("🌈 Efecto Arcoíris ACTIVADO");
    } else {
      request->send(200, "text/plain", "Efecto Arcoíris DESACTIVADO");
      Serial.println("⚫ Efecto Arcoíris DESACTIVADO");
    }
  });

  server.on("/efecto/parpadeo", HTTP_GET, [](AsyncWebServerRequest *request){
    modoParpadeo = !modoParpadeo;
    modoArcoiris = false;
    if(modoParpadeo) {
      request->send(200, "text/plain", "Efecto Parpadeo ACTIVADO");
      Serial.println("✨ Efecto Parpadeo ACTIVADO");
    } else {
      request->send(200, "text/plain", "Efecto Parpadeo DESACTIVADO");
      Serial.println("⚫ Efecto Parpadeo DESACTIVADO");
    }
  });

  // API de estado
  server.on("/estado", HTTP_GET, [](AsyncWebServerRequest *request){
    String json = "{";
    json += "\"rojo\":" + String(estadoLedRojo ? "true" : "false") + ",";
    json += "\"verde\":" + String(estadoLedVerde ? "true" : "false") + ",";
    json += "\"azul\":" + String(estadoLedAzul ? "true" : "false") + ",";
    json += "\"amarillo\":" + String(estadoLedAmarillo ? "true" : "false") + ",";
    json += "\"arcoiris\":" + String(modoArcoiris ? "true" : "false") + ",";
    json += "\"parpadeo\":" + String(modoParpadeo ? "true" : "false");
    json += "}";
    request->send(200, "application/json", json);
  });

  server.begin();
  Serial.println("🌐 Servidor web iniciado en puerto 80");
}

void controlarLED(int pin, bool estado) {
  digitalWrite(pin, estado ? HIGH : LOW);
}

void efectoArcoiris() {
  static unsigned long ultimoCambio = 0;

  if(millis() - ultimoCambio > 500) { // Cambiar cada 500ms
    // Apagar todos
    controlarLED(LED_ROJO, false);
    controlarLED(LED_VERDE, false);
    controlarLED(LED_AZUL, false);
    controlarLED(LED_AMARILLO, false);

    // Encender el siguiente en secuencia
    switch(colorActual) {
      case 0: controlarLED(LED_ROJO, true); break;
      case 1: controlarLED(LED_VERDE, true); break;
      case 2: controlarLED(LED_AZUL, true); break;
      case 3: controlarLED(LED_AMARILLO, true); break;
    }

    colorActual = (colorActual + 1) % 4;
    ultimoCambio = millis();
  }
}

void efectoParpadeo() {
  if(millis() - ultimoParpadeo > 300) { // Parpadear cada 300ms
    bool estado = digitalRead(LED_ROJO);

    controlarLED(LED_ROJO, !estado);
    controlarLED(LED_VERDE, !estado);
    controlarLED(LED_AZUL, !estado);
    controlarLED(LED_AMARILLO, !estado);

    ultimoParpadeo = millis();
  }
}

String generarPaginaWeb() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<title>🔥 Control de LEDs ESP8266</title>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<style>";
  html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
  html += "body { font-family: 'Segoe UI', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }";
  html += ".container { max-width: 800px; margin: 0 auto; background: rgba(255,255,255,0.95); border-radius: 20px; padding: 30px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }";
  html += ".header { text-align: center; margin-bottom: 30px; }";
  html += ".header h1 { color: #333; font-size: 2.5em; margin-bottom: 10px; }";
  html += ".header p { color: #666; font-size: 1.1em; }";
  html += ".status { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 30px; }";
  html += ".status-card { background: #f8f9fa; padding: 20px; border-radius: 15px; text-align: center; border-left: 5px solid #007bff; }";
  html += ".led-controls { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }";
  html += ".led-card { background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); text-align: center; }";
  html += ".led-card h3 { margin-bottom: 15px; font-size: 1.3em; }";
  html += ".led-rojo h3 { color: #dc3545; }";
  html += ".led-verde h3 { color: #28a745; }";
  html += ".led-azul h3 { color: #007bff; }";
  html += ".led-amarillo h3 { color: #ffc107; }";
  html += ".btn { padding: 12px 25px; margin: 5px; border: none; border-radius: 25px; font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; text-decoration: none; display: inline-block; }";
  html += ".btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }";
  html += ".btn-on { background: #28a745; color: white; }";
  html += ".btn-off { background: #dc3545; color: white; }";
  html += ".btn-rojo { background: #dc3545; color: white; }";
  html += ".btn-verde { background: #28a745; color: white; }";
  html += ".btn-azul { background: #007bff; color: white; }";
  html += ".btn-amarillo { background: #ffc107; color: #333; }";
  html += ".btn-especial { background: linear-gradient(45deg, #ff6b6b, #4ecdc4); color: white; font-size: 1.1em; }";
  html += ".btn-todos { background: #6c757d; color: white; }";
  html += ".efectos { background: white; padding: 25px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin-bottom: 20px; }";
  html += ".efectos h3 { text-align: center; margin-bottom: 20px; color: #333; }";
  html += ".efectos-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }";
  html += ".estado-led { width: 20px; height: 20px; border-radius: 50%; display: inline-block; margin-left: 10px; }";
  html += ".estado-on { background: #28a745; box-shadow: 0 0 10px #28a745; }";
  html += ".estado-off { background: #dc3545; }";
  html += ".footer { text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }";
  html += "@media (max-width: 600px) { .container { padding: 20px; } .header h1 { font-size: 2em; } }";
  html += "</style>";
  html += "<script>";
  html += "function actualizarEstado() {";
  html += "  fetch('/estado').then(r => r.json()).then(data => {";
  html += "    document.getElementById('estado-rojo').className = 'estado-led ' + (data.rojo ? 'estado-on' : 'estado-off');";
  html += "    document.getElementById('estado-verde').className = 'estado-led ' + (data.verde ? 'estado-on' : 'estado-off');";
  html += "    document.getElementById('estado-azul').className = 'estado-led ' + (data.azul ? 'estado-on' : 'estado-off');";
  html += "    document.getElementById('estado-amarillo').className = 'estado-led ' + (data.amarillo ? 'estado-on' : 'estado-off');";
  html += "  });";
  html += "}";
  html += "setInterval(actualizarEstado, 1000);";
  html += "window.onload = actualizarEstado;";
  html += "</script>";
  html += "</head><body>";

  html += "<div class='container'>";
  html += "<div class='header'>";
  html += "<h1>🔥 Control de LEDs</h1>";
  html += "<p>ESP8266 WiFi Controller</p>";
  html += "</div>";

  // Estado del sistema
  html += "<div class='status'>";
  html += "<div class='status-card'>";
  html += "<h4>📡 WiFi</h4>";
  html += "<p>" + String(WiFi.status() == WL_CONNECTED ? "✅ Conectado" : "❌ Desconectado") + "</p>";
  html += "<small>" + WiFi.localIP().toString() + "</small>";
  html += "</div>";
  html += "<div class='status-card'>";
  html += "<h4>⚡ Sistema</h4>";
  html += "<p>✅ Activo</p>";
  html += "<small>Uptime: " + String(millis() / 1000) + "s</small>";
  html += "</div>";
  html += "</div>";

  // Controles individuales de LEDs
  html += "<div class='led-controls'>";

  html += "<div class='led-card led-rojo'>";
  html += "<h3>🔴 LED Rojo <span id='estado-rojo' class='estado-led'></span></h3>";
  html += "<a href='/led/rojo/on' class='btn btn-rojo'>ENCENDER</a>";
  html += "<a href='/led/rojo/off' class='btn btn-off'>APAGAR</a>";
  html += "</div>";

  html += "<div class='led-card led-verde'>";
  html += "<h3>🟢 LED Verde <span id='estado-verde' class='estado-led'></span></h3>";
  html += "<a href='/led/verde/on' class='btn btn-verde'>ENCENDER</a>";
  html += "<a href='/led/verde/off' class='btn btn-off'>APAGAR</a>";
  html += "</div>";

  html += "<div class='led-card led-azul'>";
  html += "<h3>🔵 LED Azul <span id='estado-azul' class='estado-led'></span></h3>";
  html += "<a href='/led/azul/on' class='btn btn-azul'>ENCENDER</a>";
  html += "<a href='/led/azul/off' class='btn btn-off'>APAGAR</a>";
  html += "</div>";

  html += "<div class='led-card led-amarillo'>";
  html += "<h3>🟡 LED Amarillo <span id='estado-amarillo' class='estado-led'></span></h3>";
  html += "<a href='/led/amarillo/on' class='btn btn-amarillo'>ENCENDER</a>";
  html += "<a href='/led/amarillo/off' class='btn btn-off'>APAGAR</a>";
  html += "</div>";

  html += "</div>";

  // Efectos especiales
  html += "<div class='efectos'>";
  html += "<h3>✨ Efectos Especiales</h3>";
  html += "<div class='efectos-grid'>";
  html += "<a href='/efecto/arcoiris' class='btn btn-especial'>🌈 Arcoíris</a>";
  html += "<a href='/efecto/parpadeo' class='btn btn-especial'>✨ Parpadeo</a>";
  html += "<a href='/todos/on' class='btn btn-on'>🌟 Todos ON</a>";
  html += "<a href='/todos/off' class='btn btn-todos'>⚫ Todos OFF</a>";
  html += "</div>";
  html += "</div>";

  // Footer
  html += "<div class='footer'>";
  html += "<p>🔥 ESP8266 LED Controller | Hecho con ❤️</p>";
  html += "<p>Actualización automática cada segundo</p>";
  html += "</div>";

  html += "</div>";
  html += "</body></html>";

  return html;
}

void controlarLED(int pin, bool estado) {
  digitalWrite(pin, estado ? HIGH : LOW);
}

void efectoArcoiris() {
  static unsigned long ultimoCambio = 0;

  if(millis() - ultimoCambio > 500) { // Cambiar cada 500ms
    // Apagar todos
    controlarLED(LED_ROJO, false);
    controlarLED(LED_VERDE, false);
    controlarLED(LED_AZUL, false);
    controlarLED(LED_AMARILLO, false);

    // Encender el siguiente en secuencia
    switch(colorActual) {
      case 0: controlarLED(LED_ROJO, true); break;
      case 1: controlarLED(LED_VERDE, true); break;
      case 2: controlarLED(LED_AZUL, true); break;
      case 3: controlarLED(LED_AMARILLO, true); break;
    }

    colorActual = (colorActual + 1) % 4;
    ultimoCambio = millis();
  }
}

void efectoParpadeo() {
  if(millis() - ultimoParpadeo > 300) { // Parpadear cada 300ms
    bool estado = digitalRead(LED_ROJO);

    controlarLED(LED_ROJO, !estado);
    controlarLED(LED_VERDE, !estado);
    controlarLED(LED_AZUL, !estado);
    controlarLED(LED_AMARILLO, !estado);

    ultimoParpadeo = millis();
  }
}

// ¡Código limpio! Todas las funciones del controlador de LEDs están arriba.