# 🔗 Integración ESP8266 - Sistema de Seguridad Completo

## 🎯 **Rol del ESP8266 en el Sistema**

El **ESP8266** funciona como un **Nodo Sensor Remoto** que extiende las capacidades del sistema de seguridad principal, permitiendo monitorear múltiples ubicaciones de forma inalámbrica.

## 🏗️ **Arquitectura Completa del Sistema**

```
ESP32 (Maestro + IA + Web) ←→ ESP32-CAM (Vigilancia)
     ↓ (Serial)                      ↑ (WiFi)
Arduino UNO (Sensores Principales)   │
     ├── RFID RC522                  │
     ├── Lector de Huella            │
     ├── HC-SR04                     │
     └── 2x Sensores IR              │
                                     │
     ↓ (WiFi)                       │
ESP8266 (Nodo Remoto) ──────────────┘
     ├── PIR (Movimiento)
     ├── Sensor Magnético (Puerta)
     ├── DHT22 (Temp/Humedad)
     ├── Botón Pánico
     └── Sirena Local
```

## 🚀 **Funcionalidades del ESP8266**

### **📡 Sensores Integrados**
- **PIR HC-SR501**: Detección de movimiento
- **Sensor magnético**: Estado de puerta/ventana
- **DHT22**: Temperatura y humedad ambiental
- **Botón de pánico**: Alerta de emergencia remota

### **🚨 Actuadores**
- **Sirena local**: Alarma audible inmediata
- **3x LEDs**: Estado, alarma, WiFi
- **Interfaz web**: Control y monitoreo local

### **🤖 Comunicación Inteligente**
- **Envío automático** de datos cada 2 segundos
- **Heartbeat** cada 30 segundos para verificar conectividad
- **Alertas inmediatas** cuando se detectan eventos
- **Comandos remotos** desde ESP32 maestro

## 📋 **Archivos del Proyecto ESP8266**

### **Código Principal**
- **`ESP8266_Remote_Security_Node.ino`** - Código completo del nodo sensor

### **Documentación**
- **`ESP8266_Conexiones_Detalladas.md`** - Diagrama de conexiones
- **`README_ESP8266_Integration.md`** - Este archivo

## ⚙️ **Configuración Rápida**

### **1. Hardware Necesario**
```
✅ ESP8266 NodeMCU v1.0
✅ Sensor PIR HC-SR501
✅ Reed switch (sensor magnético)
✅ DHT22 (temperatura/humedad)
✅ Buzzer activo 5V
✅ Botón pulsador
✅ 3x LEDs + resistencias 220Ω
✅ Protoboard y cables
```

### **2. Configuración de Software**
```cpp
// En ESP8266_Remote_Security_Node.ino
const char* ssid = "Redmi Note 12 5G";        // ⚠️ MISMO WiFi
const char* password = "123456789";            // ⚠️ MISMO password
const char* ESP32_MASTER_IP = "*************"; // IP del ESP32 maestro
const char* NODE_ID = "ESP8266_NODO_01";       // ID único
const char* NODE_LOCATION = "Entrada_Trasera"; // Ubicación
```

### **3. Instalación**
1. **Conectar hardware** según diagrama de conexiones
2. **Configurar Arduino IDE** para ESP8266
3. **Subir código** al ESP8266
4. **Verificar conexión** en Serial Monitor
5. **Comprobar registro** en ESP32 maestro

## 🌐 **Comunicación con ESP32 Maestro**

### **Datos Enviados Automáticamente**
```json
{
  "node_id": "ESP8266_NODO_01",
  "location": "Entrada_Trasera",
  "timestamp": 123456789,
  "sensores": {
    "pir": false,
    "puerta": false,
    "temperatura": 23.5,
    "humedad": 65.2,
    "boton_panico": false
  },
  "estado": {
    "activo": true,
    "alarma": false,
    "wifi_rssi": -45,
    "memoria_libre": 45000
  }
}
```

### **Alertas de Emergencia**
```json
{
  "node_id": "ESP8266_NODO_01",
  "location": "Entrada_Trasera",
  "tipo_evento": "MOVIMIENTO_PIR",
  "timestamp": 123456789,
  "prioridad": "ALTA"
}
```

### **Comandos Recibidos del ESP32**
- `ACTIVAR_NODO` - Activar monitoreo
- `DESACTIVAR_NODO` - Desactivar monitoreo
- `ACTIVAR_ALARMA` - Activar sirena local
- `DESACTIVAR_ALARMA` - Desactivar sirena
- `TEST_SIRENA` - Probar sirena por 2 segundos

## 🎮 **Interfaces de Control**

### **Dashboard ESP32 Maestro**
- **Estado de nodos**: Lista de todos los ESP8266 conectados
- **Datos en tiempo real**: Temperatura, humedad, sensores
- **Control remoto**: Activar/desactivar nodos
- **Historial de alertas**: Eventos de todos los nodos

### **Interfaz Web Local ESP8266**
```
http://IP_DEL_ESP8266/
```
- **Estado del nodo**: Sensores, WiFi, memoria
- **Control local**: Activar/desactivar, test sirena
- **Información de conexión**: RSSI, uptime

## 🔄 **Flujo de Funcionamiento**

### **Operación Normal**
```
1. ESP8266 lee sensores cada 100ms
2. Envía datos al ESP32 cada 2 segundos
3. ESP32 procesa y almacena datos
4. Dashboard muestra estado en tiempo real
```

### **Detección de Evento**
```
1. Sensor detecta movimiento/puerta abierta
2. ESP8266 activa sirena local inmediatamente
3. Envía alerta de alta prioridad al ESP32
4. ESP32 recibe alerta y activa protocolo:
   - Toma foto con ESP32-CAM
   - Consulta IA para análisis
   - Activa alarma general si es necesario
   - Registra evento en dashboard
```

### **Botón de Pánico**
```
1. Usuario presiona botón de pánico
2. ESP8266 activa sirena local
3. Envía alerta de emergencia al ESP32
4. ESP32 activa MODO_ALERTA máximo:
   - Todas las sirenas
   - Fotos continuas
   - Notificaciones a todos los canales
```

## 📍 **Ubicaciones Recomendadas**

### **Nodo 1: Entrada Secundaria**
- **PIR**: Cobertura del área de entrada
- **Sensor puerta**: Puerta trasera/lateral
- **Botón pánico**: Accesible pero discreto

### **Nodo 2: Ventanas Importantes**
- **PIR**: Detección de intrusos por ventana
- **Sensor magnético**: Estado de ventana
- **DHT22**: Monitoreo ambiental

### **Nodo 3: Garaje/Sótano**
- **PIR**: Movimiento en área de almacenamiento
- **Sensor puerta**: Acceso al garaje
- **Sirena**: Alerta local en área aislada

### **Nodo 4: Habitación Crítica**
- **PIR**: Detección de presencia no autorizada
- **DHT22**: Monitoreo de condiciones
- **Botón pánico**: Emergencia desde habitación

## 🔧 **Configuración Avanzada**

### **Múltiples Nodos ESP8266**
```cpp
// Nodo 1
#define NODE_ID "ESP8266_ENTRADA_TRASERA"
#define NODE_LOCATION "Entrada_Trasera"

// Nodo 2  
#define NODE_ID "ESP8266_VENTANA_SALA"
#define NODE_LOCATION "Ventana_Sala"

// Nodo 3
#define NODE_ID "ESP8266_GARAJE"
#define NODE_LOCATION "Garaje"
```

### **Personalización de Sensibilidad**
```cpp
// PIR más sensible para exteriores
const unsigned long DEBOUNCE_DELAY = 50;

// PIR menos sensible para interiores
const unsigned long DEBOUNCE_DELAY = 200;

// Duración de alarma personalizada
const unsigned long DURACION_ALARMA = 15000; // 15 segundos
```

### **Configuración de Red**
```cpp
// Para redes con IP fija
WiFi.config(IPAddress(192,168,1,201), 
           IPAddress(192,168,1,1), 
           IPAddress(255,255,255,0));
```

## 🚨 **Solución de Problemas**

### **ESP8266 no se conecta al ESP32**
1. Verificar que ambos estén en la misma red WiFi
2. Comprobar IP del ESP32 maestro
3. Verificar que el ESP32 tenga las APIs habilitadas
4. Revisar firewall/router que no bloquee comunicación

### **Sensores no responden**
1. Verificar conexiones según diagrama
2. Comprobar alimentación (3V3 vs 5V)
3. Revisar pull-ups en sensores digitales
4. Test individual de cada sensor

### **Sirena no suena**
1. Verificar alimentación 5V del buzzer
2. Comprobar si necesita transistor para más corriente
3. Test directo conectando buzzer a 5V

### **Datos no llegan al ESP32**
1. Verificar conectividad WiFi de ambos dispositivos
2. Comprobar que el ESP32 esté ejecutando el servidor
3. Revisar logs en Serial Monitor de ambos dispositivos
4. Verificar formato JSON de los datos enviados

## 📊 **Monitoreo y Mantenimiento**

### **Indicadores de Estado**
- **LED WiFi**: Azul sólido = conectado, apagado = desconectado
- **LED Estado**: Verde parpadeando = nodo activo
- **LED Alarma**: Rojo parpadeando = alarma activa

### **Información de Diagnóstico**
```
http://IP_ESP8266/status
```
Devuelve JSON con:
- Estado de todos los sensores
- Información de conectividad WiFi
- Memoria libre
- Uptime del dispositivo

### **Mantenimiento Preventivo**
- **Verificar conexión WiFi** semanalmente
- **Limpiar sensores PIR** mensualmente
- **Probar sirenas** mensualmente
- **Verificar baterías** (si se usan) trimestralmente

## 🎯 **Ventajas de la Integración ESP8266**

### **✅ Beneficios**
- **Cobertura extendida**: Monitoreo de múltiples ubicaciones
- **Redundancia**: Sistema distribuido más confiable
- **Escalabilidad**: Fácil agregar más nodos
- **Costo-efectivo**: ESP8266 es económico
- **Instalación flexible**: Solo necesita WiFi y alimentación

### **🔄 Comunicación Inteligente**
- **Datos en tiempo real** al sistema principal
- **Alertas inmediatas** para eventos críticos
- **Control remoto** desde dashboard central
- **Heartbeat automático** para verificar conectividad

### **🚀 Funcionalidades Únicas**
- **Sirena local** para respuesta inmediata
- **Interfaz web propia** para configuración local
- **Sensores ambientales** (temperatura/humedad)
- **Botón de pánico** distribuido

## 🏆 **Resultado Final**

Con la integración del ESP8266, tu sistema de seguridad se convierte en una **red inteligente distribuida** que puede:

- **Monitorear múltiples ubicaciones** simultáneamente
- **Responder localmente** a eventos (sirena inmediata)
- **Comunicar centralmente** para coordinación
- **Escalar fácilmente** agregando más nodos
- **Mantener redundancia** si un nodo falla

**¡Tu hogar estará protegido desde todos los ángulos!** 🏠🔒🌐
