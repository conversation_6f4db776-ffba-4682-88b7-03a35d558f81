#ifndef CONFIG_H
#define CONFIG_H

// ===== CONFIGURACIÓN DE RED =====
// ⚠️ IMPORTANTE: Cambia estos valores por los de tu red WiFi
#define WIFI_SSID_ORIGEN "TU_WIFI_ORIGEN"        // Cambia por el nombre de tu WiFi
#define WIFI_PASSWORD_ORIGEN "TU_PASSWORD_ORIGEN" // Cambia por la contraseña de tu WiFi

// Punto de acceso (hotspot que creará el ESP8266)
#define AP_SSID "ESP8266_Hotspot"           // Nombre del hotspot que verán los dispositivos
#define AP_PASSWORD "12345678"              // Contraseña del hotspot (mínimo 8 caracteres)

// ===== CONFIGURACIÓN DE LÍMITES =====
#define LIMITE_MB 100           // Límite en MB por cliente
#define MAX_CLIENTES 4          // Máximo número de clientes simultáneos

// ===== CONFIGURACIÓN DE MONITOREO =====
#define INTERVALO_VERIFICACION 5000  // Intervalo de verificación en ms (5 segundos)
#define AUTO_REFRESH_WEB 10000       // Auto-refresh de la página web en ms (10 segundos)

// ===== CONFIGURACIÓN DE RED AP =====
#define AP_IP_ADDRESS 192, 168, 4, 1
#define AP_GATEWAY 192, 168, 4, 1
#define AP_SUBNET 255, 255, 255, 0

// ===== CONFIGURACIÓN DE SERVIDOR WEB =====
#define WEB_SERVER_PORT 80

// ===== CONFIGURACIÓN DE DEBUG =====
#define SERIAL_BAUD_RATE 115200
#define DEBUG_ENABLED true

// ===== CONFIGURACIÓN AVANZADA =====
#define EEPROM_SIZE 512
#define WIFI_CONNECT_TIMEOUT 20  // Timeout en segundos para conexión WiFi
#define CLIENT_TIMEOUT 300000    // Timeout para clientes inactivos (5 minutos)

// ===== SIMULACIÓN DE DATOS =====
// Para demostración, se simula el uso de datos
#define SIMULATE_DATA_USAGE true
#define MIN_BYTES_PER_CHECK 1024    // Mínimo bytes simulados por verificación
#define MAX_BYTES_PER_CHECK 8192    // Máximo bytes simulados por verificación

#endif // CONFIG_H
