/*
 * 🏠 SISTEMA DE SEGURIDAD INTELIGENTE - ESP32 MAESTRO
 * 
 * Funcionalidades:
 * - Control de acceso multi-factor (RFID + Huella + IR)
 * - Integración con IA para análisis inteligente
 * - Comunicación con ESP32-CAM para vigilancia
 * - Interfaz web completa con dashboard
 * - Sistema de alertas en tiempo real
 * - Base de datos de usuarios
 * - Chat con IA para control por voz/texto
 * 
 * Hardware conectado:
 * - Arduino UNO (via Serial) - Hub de sensores
 * - ESP32-CAM (via WiFi) - Cámara de seguridad
 * - Buzzer, LEDs, Relé para puerta
 */

#include <WiFi.h>
#include <WebServer.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include <time.h>
#include <ESPAsyncWebServer.h>
#include <AsyncTCP.h>

// ===== CONFIGURACIÓN WiFi =====
const char* ssid = "Redmi Note 12 5G";        // ⚠️ CAMBIAR
const char* password = "123456789";            // ⚠️ CAMBIAR

// ===== CONFIGURACIÓN API IA =====
const char* AI_API_URL = "https://api.openai.com/v1/chat/completions";  // ⚠️ CAMBIAR
const char* AI_API_KEY = "tu-api-key-aqui";                             // ⚠️ CAMBIAR
const char* AI_MODEL = "gpt-3.5-turbo";

// ===== CONFIGURACIÓN ESP32-CAM =====
const char* ESP32CAM_IP = "*************";    // IP fija del ESP32-CAM
const int ESP32CAM_PORT = 80;

// ===== PINES ESP32 =====
const int PIN_BUZZER = 2;
const int PIN_LED_VERDE = 4;    // Acceso permitido
const int PIN_LED_ROJO = 5;     // Acceso denegado
const int PIN_LED_AZUL = 18;    // Sistema activo
const int PIN_RELE_PUERTA = 19; // Control de cerradura
const int PIN_BOTON_PANICO = 21; // Botón de pánico

// ===== COMUNICACIÓN SERIAL =====
#define SERIAL_ARDUINO Serial2  // Comunicación con Arduino UNO
const int RX_PIN = 16;
const int TX_PIN = 17;

// ===== SERVIDOR WEB =====
AsyncWebServer server(80);

// ===== ESTRUCTURAS DE DATOS =====
struct Usuario {
  String nombre;
  String rfid_id;
  String huella_id;
  bool activo;
  String permisos;
  unsigned long ultimo_acceso;
};

struct EventoAcceso {
  unsigned long timestamp;
  String usuario;
  String metodo;
  bool exitoso;
  String foto_url;
  String analisis_ia;
};

struct SensorData {
  bool ir1_detectado;
  bool ir2_detectado;
  int distancia_ultrasonico;
  String rfid_detectado;
  bool huella_detectada;
  String huella_id;
  unsigned long timestamp;
};

// ===== VARIABLES GLOBALES =====
std::vector<Usuario> usuarios;
std::vector<EventoAcceso> eventos;
SensorData sensores_actuales;

bool sistema_armado = true;
bool modo_panico = false;
bool puerta_abierta = false;
unsigned long tiempo_puerta_abierta = 0;
const unsigned long TIMEOUT_PUERTA = 10000; // 10 segundos

// Estados del sistema
enum EstadoSistema {
  ESPERANDO,
  DETECTANDO_PROXIMIDAD,
  ESPERANDO_IDENTIFICACION,
  PROCESANDO_ACCESO,
  ACCESO_CONCEDIDO,
  ACCESO_DENEGADO,
  MODO_ALERTA
};

EstadoSistema estado_actual = ESPERANDO;

// ===== CONFIGURACIÓN INICIAL =====
void setup() {
  Serial.begin(115200);
  SERIAL_ARDUINO.begin(9600, SERIAL_8N1, RX_PIN, TX_PIN);
  
  Serial.println("\n🏠 SISTEMA DE SEGURIDAD INTELIGENTE");
  Serial.println("====================================");
  
  // Configurar pines
  configurarPines();
  
  // Inicializar SPIFFS para almacenamiento
  if(!SPIFFS.begin(true)) {
    Serial.println("❌ Error montando SPIFFS");
    return;
  }
  
  // Conectar WiFi
  conectarWiFi();
  
  // Configurar tiempo (NTP)
  configurarTiempo();
  
  // Cargar usuarios desde memoria
  cargarUsuarios();
  
  // Configurar servidor web
  configurarServidorWeb();
  
  // Inicializar comunicación con ESP32-CAM
  inicializarESP32CAM();
  
  // Configurar interrupciones
  attachInterrupt(digitalPinToInterrupt(PIN_BOTON_PANICO), botonPanico, FALLING);
  
  Serial.println("✅ Sistema iniciado correctamente");
  Serial.print("🌐 IP: ");
  Serial.println(WiFi.localIP());
  Serial.println("====================================");
  
  // Señal de inicio
  indicarInicioSistema();
}

// ===== LOOP PRINCIPAL =====
void loop() {
  // Leer datos de sensores desde Arduino
  leerSensoresArduino();
  
  // Procesar estado del sistema
  procesarEstadoSistema();
  
  // Verificar timeout de puerta
  verificarTimeoutPuerta();
  
  // Procesar comandos web
  // El servidor async se maneja automáticamente
  
  // Verificar botón de pánico
  if(modo_panico) {
    procesarModoPanico();
  }

  // Verificar estado de nodos ESP8266
  static unsigned long ultima_verificacion_nodos = 0;
  if(millis() - ultima_verificacion_nodos > 30000) { // Cada 30 segundos
    verificarNodosESP8266();
    ultima_verificacion_nodos = millis();
  }

  delay(100);
}

void configurarPines() {
  pinMode(PIN_BUZZER, OUTPUT);
  pinMode(PIN_LED_VERDE, OUTPUT);
  pinMode(PIN_LED_ROJO, OUTPUT);
  pinMode(PIN_LED_AZUL, OUTPUT);
  pinMode(PIN_RELE_PUERTA, OUTPUT);
  pinMode(PIN_BOTON_PANICO, INPUT_PULLUP);
  
  // Estado inicial
  digitalWrite(PIN_LED_AZUL, HIGH);  // Sistema activo
  digitalWrite(PIN_RELE_PUERTA, LOW); // Puerta cerrada
  
  Serial.println("🔧 Pines configurados");
}

void conectarWiFi() {
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);
  
  Serial.print("🔄 Conectando a WiFi");
  while(WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  
  Serial.println("\n✅ WiFi conectado");
  Serial.print("📡 IP: ");
  Serial.println(WiFi.localIP());
}

void configurarTiempo() {
  configTime(0, 0, "pool.ntp.org");
  Serial.println("⏰ Tiempo configurado");
}

void indicarInicioSistema() {
  // Secuencia de LEDs de inicio
  for(int i = 0; i < 3; i++) {
    digitalWrite(PIN_LED_VERDE, HIGH);
    delay(200);
    digitalWrite(PIN_LED_VERDE, LOW);
    digitalWrite(PIN_LED_ROJO, HIGH);
    delay(200);
    digitalWrite(PIN_LED_ROJO, LOW);
    digitalWrite(PIN_LED_AZUL, HIGH);
    delay(200);
    digitalWrite(PIN_LED_AZUL, LOW);
  }
  
  // Buzzer de confirmación
  tone(PIN_BUZZER, 1000, 200);
  delay(300);
  tone(PIN_BUZZER, 1500, 200);
  
  digitalWrite(PIN_LED_AZUL, HIGH); // Sistema listo
}

void leerSensoresArduino() {
  // Enviar comando de lectura al Arduino
  SERIAL_ARDUINO.println("READ_SENSORS");
  
  // Esperar respuesta
  if(SERIAL_ARDUINO.available()) {
    String respuesta = SERIAL_ARDUINO.readStringUntil('\n');
    procesarRespuestaArduino(respuesta);
  }
}

void procesarRespuestaArduino(String datos) {
  // Formato esperado: "IR1:1,IR2:0,DIST:25,RFID:12345678,HUELLA:1,HUELLA_ID:001"
  
  // Parsear datos usando JSON para mayor robustez
  DynamicJsonDocument doc(1024);
  deserializeJson(doc, datos);
  
  sensores_actuales.ir1_detectado = doc["ir1"];
  sensores_actuales.ir2_detectado = doc["ir2"];
  sensores_actuales.distancia_ultrasonico = doc["distancia"];
  sensores_actuales.rfid_detectado = doc["rfid"].as<String>();
  sensores_actuales.huella_detectada = doc["huella"];
  sensores_actuales.huella_id = doc["huella_id"].as<String>();
  sensores_actuales.timestamp = millis();
  
  // Debug
  Serial.println("📊 Sensores: IR1=" + String(sensores_actuales.ir1_detectado) + 
                 " IR2=" + String(sensores_actuales.ir2_detectado) + 
                 " DIST=" + String(sensores_actuales.distancia_ultrasonico) + 
                 " RFID=" + sensores_actuales.rfid_detectado);
}

void procesarEstadoSistema() {
  switch(estado_actual) {
    case ESPERANDO:
      if(sensores_actuales.ir1_detectado || sensores_actuales.ir2_detectado) {
        estado_actual = DETECTANDO_PROXIMIDAD;
        Serial.println("👤 Proximidad detectada");
        digitalWrite(PIN_LED_AZUL, LOW);
        digitalWrite(PIN_LED_VERDE, HIGH);
      }
      break;
      
    case DETECTANDO_PROXIMIDAD:
      if(sensores_actuales.distancia_ultrasonico < 50) { // Menos de 50cm
        estado_actual = ESPERANDO_IDENTIFICACION;
        Serial.println("🔍 Esperando identificación...");
        tone(PIN_BUZZER, 800, 100); // Beep de confirmación
      }
      break;
      
    case ESPERANDO_IDENTIFICACION:
      if(sensores_actuales.rfid_detectado != "" || sensores_actuales.huella_detectada) {
        estado_actual = PROCESANDO_ACCESO;
        procesarIntentAcceso();
      }
      break;
      
    case PROCESANDO_ACCESO:
      // Se procesa en procesarIntentAcceso()
      break;
      
    case ACCESO_CONCEDIDO:
      abrirPuerta();
      estado_actual = ESPERANDO;
      break;
      
    case ACCESO_DENEGADO:
      denegarAcceso();
      estado_actual = ESPERANDO;
      break;
      
    case MODO_ALERTA:
      procesarModoAlerta();
      break;
  }
}

void procesarIntentAcceso() {
  Serial.println("🔐 Procesando intento de acceso...");

  String usuario_encontrado = "";
  bool acceso_valido = false;

  // Verificar RFID
  if(sensores_actuales.rfid_detectado != "") {
    usuario_encontrado = buscarUsuarioPorRFID(sensores_actuales.rfid_detectado);
    if(usuario_encontrado != "") {
      acceso_valido = true;
      Serial.println("✅ RFID válido: " + usuario_encontrado);
    }
  }

  // Verificar huella (si no hay RFID válido)
  if(!acceso_valido && sensores_actuales.huella_detectada) {
    usuario_encontrado = buscarUsuarioPorHuella(sensores_actuales.huella_id);
    if(usuario_encontrado != "") {
      acceso_valido = true;
      Serial.println("✅ Huella válida: " + usuario_encontrado);
    }
  }

  // Tomar foto para análisis
  String foto_url = solicitarFotoESP32CAM();

  // Consultar IA para análisis adicional
  String analisis_ia = consultarIA(usuario_encontrado, foto_url, acceso_valido);

  // Registrar evento
  EventoAcceso evento;
  evento.timestamp = millis();
  evento.usuario = usuario_encontrado != "" ? usuario_encontrado : "Desconocido";
  evento.metodo = sensores_actuales.rfid_detectado != "" ? "RFID" : "Huella";
  evento.exitoso = acceso_valido;
  evento.foto_url = foto_url;
  evento.analisis_ia = analisis_ia;
  eventos.push_back(evento);

  // Decidir acción final
  if(acceso_valido && analisis_ia.indexOf("PERMITIR") >= 0) {
    estado_actual = ACCESO_CONCEDIDO;
  } else {
    estado_actual = ACCESO_DENEGADO;
    if(analisis_ia.indexOf("ALERTA") >= 0) {
      estado_actual = MODO_ALERTA;
    }
  }
}

String buscarUsuarioPorRFID(String rfid_id) {
  for(const auto& usuario : usuarios) {
    if(usuario.rfid_id == rfid_id && usuario.activo) {
      return usuario.nombre;
    }
  }
  return "";
}

String buscarUsuarioPorHuella(String huella_id) {
  for(const auto& usuario : usuarios) {
    if(usuario.huella_id == huella_id && usuario.activo) {
      return usuario.nombre;
    }
  }
  return "";
}

void abrirPuerta() {
  Serial.println("🚪 ACCESO CONCEDIDO - Abriendo puerta");

  // Indicadores visuales y sonoros
  digitalWrite(PIN_LED_VERDE, HIGH);
  digitalWrite(PIN_LED_ROJO, LOW);
  tone(PIN_BUZZER, 1200, 300);
  delay(400);
  tone(PIN_BUZZER, 1500, 300);

  // Abrir cerradura
  digitalWrite(PIN_RELE_PUERTA, HIGH);
  puerta_abierta = true;
  tiempo_puerta_abierta = millis();

  // Notificar a la web
  notificarEventoWeb("acceso_concedido");

  delay(2000);
  digitalWrite(PIN_LED_VERDE, LOW);
  digitalWrite(PIN_LED_AZUL, HIGH);
}

void denegarAcceso() {
  Serial.println("🚫 ACCESO DENEGADO");

  // Indicadores de acceso denegado
  for(int i = 0; i < 3; i++) {
    digitalWrite(PIN_LED_ROJO, HIGH);
    tone(PIN_BUZZER, 400, 200);
    delay(300);
    digitalWrite(PIN_LED_ROJO, LOW);
    delay(200);
  }

  // Notificar evento
  notificarEventoWeb("acceso_denegado");

  digitalWrite(PIN_LED_AZUL, HIGH);
}

void procesarModoAlerta() {
  Serial.println("🚨 MODO ALERTA ACTIVADO");

  // Alerta visual y sonora intensa
  static unsigned long ultimo_parpadeo = 0;
  if(millis() - ultimo_parpadeo > 200) {
    digitalWrite(PIN_LED_ROJO, !digitalRead(PIN_LED_ROJO));
    tone(PIN_BUZZER, 1000, 100);
    ultimo_parpadeo = millis();
  }

  // Enviar alerta a todos los canales
  enviarAlertaCompleta();

  // Salir del modo alerta después de 30 segundos o intervención manual
  static unsigned long inicio_alerta = millis();
  if(millis() - inicio_alerta > 30000) {
    estado_actual = ESPERANDO;
    digitalWrite(PIN_LED_ROJO, LOW);
    digitalWrite(PIN_LED_AZUL, HIGH);
    Serial.println("⏰ Modo alerta finalizado por timeout");
  }
}

void verificarTimeoutPuerta() {
  if(puerta_abierta && (millis() - tiempo_puerta_abierta > TIMEOUT_PUERTA)) {
    // Cerrar puerta automáticamente
    digitalWrite(PIN_RELE_PUERTA, LOW);
    puerta_abierta = false;
    Serial.println("🔒 Puerta cerrada automáticamente");

    // Beep de confirmación
    tone(PIN_BUZZER, 800, 200);
  }
}

void IRAM_ATTR botonPanico() {
  modo_panico = true;
}

void procesarModoPanico() {
  Serial.println("🆘 BOTÓN DE PÁNICO ACTIVADO");

  estado_actual = MODO_ALERTA;

  // Tomar foto de emergencia
  String foto_emergencia = solicitarFotoESP32CAM();

  // Consultar IA sobre la situación
  String analisis_emergencia = consultarIA("EMERGENCIA", foto_emergencia, false);

  // Registrar evento de pánico
  EventoAcceso evento_panico;
  evento_panico.timestamp = millis();
  evento_panico.usuario = "BOTON_PANICO";
  evento_panico.metodo = "EMERGENCIA";
  evento_panico.exitoso = false;
  evento_panico.foto_url = foto_emergencia;
  evento_panico.analisis_ia = analisis_emergencia;
  eventos.push_back(evento_panico);

  // Enviar alerta máxima
  enviarAlertaEmergencia();

  modo_panico = false; // Reset después de procesar
}

String solicitarFotoESP32CAM() {
  HTTPClient http;
  http.begin("http://" + String(ESP32CAM_IP) + "/capture");

  int httpResponseCode = http.GET();
  String foto_url = "";

  if(httpResponseCode == 200) {
    foto_url = "http://" + String(ESP32CAM_IP) + "/photo/" + String(millis()) + ".jpg";
    Serial.println("📸 Foto capturada: " + foto_url);
  } else {
    Serial.println("❌ Error capturando foto: " + String(httpResponseCode));
    foto_url = "error";
  }

  http.end();
  return foto_url;
}

String consultarIA(String usuario, String foto_url, bool acceso_valido) {
  HTTPClient http;
  http.begin(AI_API_URL);
  http.addHeader("Content-Type", "application/json");
  http.addHeader("Authorization", "Bearer " + String(AI_API_KEY));

  // Crear prompt para la IA
  String prompt = "Analiza este evento de seguridad:\n";
  prompt += "Usuario: " + usuario + "\n";
  prompt += "Acceso válido: " + String(acceso_valido ? "Sí" : "No") + "\n";
  prompt += "Foto: " + foto_url + "\n";
  prompt += "Hora: " + obtenerTiempoActual() + "\n";
  prompt += "Sensores IR: " + String(sensores_actuales.ir1_detectado) + ", " + String(sensores_actuales.ir2_detectado) + "\n";
  prompt += "Distancia: " + String(sensores_actuales.distancia_ultrasonico) + "cm\n\n";
  prompt += "Responde con: PERMITIR, DENEGAR, o ALERTA seguido de tu análisis.";

  // Crear JSON request
  DynamicJsonDocument doc(2048);
  doc["model"] = AI_MODEL;
  doc["messages"][0]["role"] = "system";
  doc["messages"][0]["content"] = "Eres un sistema de seguridad IA. Analiza eventos y decide acciones.";
  doc["messages"][1]["role"] = "user";
  doc["messages"][1]["content"] = prompt;
  doc["max_tokens"] = 150;
  doc["temperature"] = 0.3;

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);
  String respuesta = "PERMITIR - Análisis IA no disponible";

  if(httpResponseCode == 200) {
    String response = http.getString();
    DynamicJsonDocument responseDoc(2048);
    deserializeJson(responseDoc, response);

    if(responseDoc["choices"][0]["message"]["content"]) {
      respuesta = responseDoc["choices"][0]["message"]["content"].as<String>();
      Serial.println("🤖 IA: " + respuesta);
    }
  } else {
    Serial.println("❌ Error consultando IA: " + String(httpResponseCode));
  }

  http.end();
  return respuesta;
}

void configurarServidorWeb() {
  // Página principal - Dashboard
  server.on("/", HTTP_GET, [](AsyncWebServerRequest *request){
    request->send(200, "text/html", generarDashboard());
  });

  // API - Estado del sistema
  server.on("/api/estado", HTTP_GET, [](AsyncWebServerRequest *request){
    String json = generarEstadoJSON();
    request->send(200, "application/json", json);
  });

  // API - Eventos recientes
  server.on("/api/eventos", HTTP_GET, [](AsyncWebServerRequest *request){
    String json = generarEventosJSON();
    request->send(200, "application/json", json);
  });

  // API - Usuarios
  server.on("/api/usuarios", HTTP_GET, [](AsyncWebServerRequest *request){
    String json = generarUsuariosJSON();
    request->send(200, "application/json", json);
  });

  // API - Agregar usuario
  server.on("/api/usuarios", HTTP_POST, [](AsyncWebServerRequest *request){
    // Procesar datos del formulario
    if(request->hasParam("nombre", true) && request->hasParam("rfid", true)) {
      String nombre = request->getParam("nombre", true)->value();
      String rfid = request->getParam("rfid", true)->value();
      String huella = request->hasParam("huella", true) ? request->getParam("huella", true)->value() : "";

      agregarUsuario(nombre, rfid, huella);
      request->send(200, "application/json", "{\"status\":\"success\"}");
    } else {
      request->send(400, "application/json", "{\"status\":\"error\",\"message\":\"Datos incompletos\"}");
    }
  });

  // API - Chat con IA
  server.on("/api/chat", HTTP_POST, [](AsyncWebServerRequest *request){
    if(request->hasParam("mensaje", true)) {
      String mensaje = request->getParam("mensaje", true)->value();
      String respuesta = procesarComandoIA(mensaje);
      request->send(200, "application/json", "{\"respuesta\":\"" + respuesta + "\"}");
    } else {
      request->send(400, "application/json", "{\"error\":\"Mensaje requerido\"}");
    }
  });

  // Control manual
  server.on("/api/abrir-puerta", HTTP_POST, [](AsyncWebServerRequest *request){
    if(request->hasParam("codigo", true)) {
      String codigo = request->getParam("codigo", true)->value();
      if(codigo == "1234") { // Código maestro
        abrirPuerta();
        request->send(200, "application/json", "{\"status\":\"success\"}");
      } else {
        request->send(403, "application/json", "{\"status\":\"error\",\"message\":\"Código incorrecto\"}");
      }
    }
  });

  // Armar/desarmar sistema
  server.on("/api/sistema", HTTP_POST, [](AsyncWebServerRequest *request){
    if(request->hasParam("accion", true)) {
      String accion = request->getParam("accion", true)->value();
      if(accion == "armar") {
        sistema_armado = true;
        request->send(200, "application/json", "{\"status\":\"armado\"}");
      } else if(accion == "desarmar") {
        sistema_armado = false;
        request->send(200, "application/json", "{\"status\":\"desarmado\"}");
      }
    }
  });

  // APIs para nodos ESP8266
  server.on("/api/nodo-sensor", HTTP_POST, [](AsyncWebServerRequest *request){}, NULL,
    [](AsyncWebServerRequest *request, uint8_t *data, size_t len, size_t index, size_t total){
      String body = String((char*)data).substring(0, len);
      procesarDatosNodoESP8266(body);
      request->send(200, "application/json", "{\"status\":\"ok\",\"comando\":\"NINGUNO\"}");
    });

  server.on("/api/alerta-nodo", HTTP_POST, [](AsyncWebServerRequest *request){}, NULL,
    [](AsyncWebServerRequest *request, uint8_t *data, size_t len, size_t index, size_t total){
      String body = String((char*)data).substring(0, len);
      procesarAlertaNodoESP8266(body);
      request->send(200, "application/json", "{\"status\":\"alerta_recibida\"}");
    });

  server.on("/api/heartbeat", HTTP_POST, [](AsyncWebServerRequest *request){}, NULL,
    [](AsyncWebServerRequest *request, uint8_t *data, size_t len, size_t index, size_t total){
      String body = String((char*)data).substring(0, len);
      procesarHeartbeatNodo(body);
      request->send(200, "application/json", "{\"status\":\"heartbeat_ok\"}");
    });

  server.on("/api/registrar-nodo", HTTP_POST, [](AsyncWebServerRequest *request){}, NULL,
    [](AsyncWebServerRequest *request, uint8_t *data, size_t len, size_t index, size_t total){
      String body = String((char*)data).substring(0, len);
      registrarNuevoNodo(body);
      request->send(200, "application/json", "{\"status\":\"nodo_registrado\"}");
    });

  // API para listar nodos ESP8266
  server.on("/api/nodos", HTTP_GET, [](AsyncWebServerRequest *request){
    DynamicJsonDocument doc(1024);

    for(int i = 0; i < nodos_esp8266.size(); i++) {
      doc["nodos"][i]["node_id"] = nodos_esp8266[i].node_id;
      doc["nodos"][i]["location"] = nodos_esp8266[i].location;
      doc["nodos"][i]["ip"] = nodos_esp8266[i].ip;
      doc["nodos"][i]["activo"] = nodos_esp8266[i].activo;
      doc["nodos"][i]["pir"] = nodos_esp8266[i].pir_detectado;
      doc["nodos"][i]["puerta"] = nodos_esp8266[i].puerta_abierta;
      doc["nodos"][i]["temperatura"] = nodos_esp8266[i].temperatura;
      doc["nodos"][i]["humedad"] = nodos_esp8266[i].humedad;
      doc["nodos"][i]["ultimo_heartbeat"] = nodos_esp8266[i].ultimo_heartbeat;
    }

    String json;
    serializeJson(doc, json);
    request->send(200, "application/json", json);
  });

  // Servir archivos estáticos
  server.serveStatic("/", SPIFFS, "/");

  server.begin();
  Serial.println("🌐 Servidor web iniciado");
}

String generarDashboard() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<title>🏠 Sistema de Seguridad Inteligente</title>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<style>";
  html += "* { margin: 0; padding: 0; box-sizing: border-box; }";
  html += "body { font-family: 'Segoe UI', Arial, sans-serif; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); min-height: 100vh; color: white; }";
  html += ".container { max-width: 1200px; margin: 0 auto; padding: 20px; }";
  html += ".header { text-align: center; margin-bottom: 30px; }";
  html += ".header h1 { font-size: 3em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }";
  html += ".dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }";
  html += ".card { background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 15px; padding: 25px; border: 1px solid rgba(255,255,255,0.2); }";
  html += ".card h3 { margin-bottom: 15px; font-size: 1.5em; }";
  html += ".status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }";
  html += ".status-online { background: #4CAF50; box-shadow: 0 0 10px #4CAF50; }";
  html += ".status-offline { background: #f44336; }";
  html += ".btn { background: linear-gradient(45deg, #4CAF50, #45a049); color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: pointer; font-size: 1em; margin: 5px; transition: all 0.3s; }";
  html += ".btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.3); }";
  html += ".btn-danger { background: linear-gradient(45deg, #f44336, #d32f2f); }";
  html += ".btn-warning { background: linear-gradient(45deg, #ff9800, #f57c00); }";
  html += ".eventos { max-height: 300px; overflow-y: auto; }";
  html += ".evento { background: rgba(255,255,255,0.05); margin: 10px 0; padding: 15px; border-radius: 10px; border-left: 4px solid #4CAF50; }";
  html += ".evento.denegado { border-left-color: #f44336; }";
  html += ".chat-container { background: rgba(0,0,0,0.2); border-radius: 15px; padding: 20px; }";
  html += ".chat-messages { height: 200px; overflow-y: auto; background: rgba(255,255,255,0.05); border-radius: 10px; padding: 15px; margin-bottom: 15px; }";
  html += ".chat-input { display: flex; gap: 10px; }";
  html += ".chat-input input { flex: 1; padding: 12px; border: none; border-radius: 25px; background: rgba(255,255,255,0.1); color: white; }";
  html += ".chat-input input::placeholder { color: rgba(255,255,255,0.7); }";
  html += "@media (max-width: 768px) { .header h1 { font-size: 2em; } .dashboard { grid-template-columns: 1fr; } }";
  html += "</style>";
  html += "<script>";
  html += "function actualizarDatos() {";
  html += "  fetch('/api/estado').then(r => r.json()).then(data => {";
  html += "    document.getElementById('sistema-estado').innerHTML = data.armado ? '🔒 ARMADO' : '🔓 DESARMADO';";
  html += "    document.getElementById('usuarios-conectados').innerHTML = data.usuarios_activos;";
  html += "    document.getElementById('eventos-hoy').innerHTML = data.eventos_hoy;";
  html += "  });";
  html += "  fetch('/api/eventos').then(r => r.json()).then(data => {";
  html += "    let html = '';";
  html += "    data.eventos.forEach(evento => {";
  html += "      html += `<div class='evento ${evento.exitoso ? '' : 'denegado'}'>`;";
  html += "      html += `<strong>${evento.usuario}</strong> - ${evento.metodo}<br>`;";
  html += "      html += `<small>${new Date(evento.timestamp).toLocaleString()}</small>`;";
  html += "      html += `</div>`;";
  html += "    });";
  html += "    document.getElementById('eventos-lista').innerHTML = html;";
  html += "  });";
  html += "}";
  html += "function enviarComandoIA() {";
  html += "  const input = document.getElementById('chat-input');";
  html += "  const mensaje = input.value;";
  html += "  if(!mensaje) return;";
  html += "  const formData = new FormData();";
  html += "  formData.append('mensaje', mensaje);";
  html += "  fetch('/api/chat', { method: 'POST', body: formData })";
  html += "  .then(r => r.json()).then(data => {";
  html += "    const chat = document.getElementById('chat-messages');";
  html += "    chat.innerHTML += `<div><strong>Tú:</strong> ${mensaje}</div>`;";
  html += "    chat.innerHTML += `<div><strong>🤖 IA:</strong> ${data.respuesta}</div>`;";
  html += "    chat.scrollTop = chat.scrollHeight;";
  html += "    input.value = '';";
  html += "  });";
  html += "}";
  html += "function abrirPuerta() {";
  html += "  const codigo = prompt('Ingresa el código maestro:');";
  html += "  if(codigo) {";
  html += "    const formData = new FormData();";
  html += "    formData.append('codigo', codigo);";
  html += "    fetch('/api/abrir-puerta', { method: 'POST', body: formData })";
  html += "    .then(r => r.json()).then(data => {";
  html += "      alert(data.status === 'success' ? 'Puerta abierta' : 'Código incorrecto');";
  html += "    });";
  html += "  }";
  html += "}";
  html += "setInterval(actualizarDatos, 2000);";
  html += "window.onload = actualizarDatos;";
  html += "</script>";
  html += "</head><body>";

  html += "<div class='container'>";
  html += "<div class='header'>";
  html += "<h1>🏠 Sistema de Seguridad</h1>";
  html += "<p>Control Inteligente con IA</p>";
  html += "</div>";

  html += "<div class='dashboard'>";

  // Card de estado del sistema
  html += "<div class='card'>";
  html += "<h3>📊 Estado del Sistema</h3>";
  html += "<p><span class='status-indicator status-online'></span>Sistema: <span id='sistema-estado'>🔒 ARMADO</span></p>";
  html += "<p><span class='status-indicator status-online'></span>ESP32-CAM: Conectada</p>";
  html += "<p><span class='status-indicator status-online'></span>Arduino: Conectado</p>";
  html += "<p>👥 Usuarios activos: <span id='usuarios-conectados'>0</span></p>";
  html += "<p>📅 Eventos hoy: <span id='eventos-hoy'>0</span></p>";
  html += "</div>";

  // Card de controles
  html += "<div class='card'>";
  html += "<h3>🎮 Controles</h3>";
  html += "<button class='btn' onclick='abrirPuerta()'>🚪 Abrir Puerta</button>";
  html += "<button class='btn btn-warning' onclick='toggleSistema()'>🔄 Toggle Sistema</button>";
  html += "<button class='btn btn-danger' onclick='activarAlarma()'>🚨 Alarma</button>";
  html += "</div>";

  // Card de eventos recientes
  html += "<div class='card'>";
  html += "<h3>📋 Eventos Recientes</h3>";
  html += "<div class='eventos' id='eventos-lista'>";
  html += "Cargando eventos...";
  html += "</div>";
  html += "</div>";

  // Card de chat con IA
  html += "<div class='card'>";
  html += "<h3>🤖 Chat con IA</h3>";
  html += "<div class='chat-container'>";
  html += "<div class='chat-messages' id='chat-messages'>";
  html += "<div><strong>🤖 IA:</strong> ¡Hola! Soy tu asistente de seguridad. Puedes preguntarme sobre el estado del sistema o darme comandos.</div>";
  html += "</div>";
  html += "<div class='chat-input'>";
  html += "<input type='text' id='chat-input' placeholder='Escribe tu mensaje...' onkeypress='if(event.key===\"Enter\") enviarComandoIA()'>";
  html += "<button class='btn' onclick='enviarComandoIA()'>Enviar</button>";
  html += "</div>";
  html += "</div>";
  html += "</div>";

  html += "</div>";
  html += "</div>";
  html += "</body></html>";

  return html;
}

String generarEstadoJSON() {
  DynamicJsonDocument doc(1024);

  doc["armado"] = sistema_armado;
  doc["puerta_abierta"] = puerta_abierta;
  doc["modo_panico"] = modo_panico;
  doc["usuarios_activos"] = usuarios.size();
  doc["eventos_hoy"] = contarEventosHoy();
  doc["estado_actual"] = estadoToString(estado_actual);
  doc["sensores"]["ir1"] = sensores_actuales.ir1_detectado;
  doc["sensores"]["ir2"] = sensores_actuales.ir2_detectado;
  doc["sensores"]["distancia"] = sensores_actuales.distancia_ultrasonico;
  doc["timestamp"] = millis();

  String json;
  serializeJson(doc, json);
  return json;
}

String generarEventosJSON() {
  DynamicJsonDocument doc(2048);

  // Mostrar últimos 10 eventos
  int inicio = max(0, (int)eventos.size() - 10);
  for(int i = inicio; i < eventos.size(); i++) {
    doc["eventos"][i - inicio]["timestamp"] = eventos[i].timestamp;
    doc["eventos"][i - inicio]["usuario"] = eventos[i].usuario;
    doc["eventos"][i - inicio]["metodo"] = eventos[i].metodo;
    doc["eventos"][i - inicio]["exitoso"] = eventos[i].exitoso;
    doc["eventos"][i - inicio]["foto_url"] = eventos[i].foto_url;
    doc["eventos"][i - inicio]["analisis_ia"] = eventos[i].analisis_ia;
  }

  String json;
  serializeJson(doc, json);
  return json;
}

String generarUsuariosJSON() {
  DynamicJsonDocument doc(1024);

  for(int i = 0; i < usuarios.size(); i++) {
    doc["usuarios"][i]["nombre"] = usuarios[i].nombre;
    doc["usuarios"][i]["rfid_id"] = usuarios[i].rfid_id;
    doc["usuarios"][i]["huella_id"] = usuarios[i].huella_id;
    doc["usuarios"][i]["activo"] = usuarios[i].activo;
    doc["usuarios"][i]["permisos"] = usuarios[i].permisos;
    doc["usuarios"][i]["ultimo_acceso"] = usuarios[i].ultimo_acceso;
  }

  String json;
  serializeJson(doc, json);
  return json;
}

String procesarComandoIA(String mensaje) {
  // Crear prompt específico para comandos del sistema
  String prompt = "Usuario dice: '" + mensaje + "'\n\n";
  prompt += "Estado actual del sistema:\n";
  prompt += "- Sistema armado: " + String(sistema_armado ? "Sí" : "No") + "\n";
  prompt += "- Puerta abierta: " + String(puerta_abierta ? "Sí" : "No") + "\n";
  prompt += "- Usuarios registrados: " + String(usuarios.size()) + "\n";
  prompt += "- Eventos hoy: " + String(contarEventosHoy()) + "\n";
  prompt += "- Sensores IR: " + String(sensores_actuales.ir1_detectado) + ", " + String(sensores_actuales.ir2_detectado) + "\n";
  prompt += "- Distancia: " + String(sensores_actuales.distancia_ultrasonico) + "cm\n\n";
  prompt += "Responde como asistente de seguridad. Si es un comando (abrir puerta, armar sistema, etc.), indica si se puede ejecutar y cómo.";

  HTTPClient http;
  http.begin(AI_API_URL);
  http.addHeader("Content-Type", "application/json");
  http.addHeader("Authorization", "Bearer " + String(AI_API_KEY));

  DynamicJsonDocument doc(2048);
  doc["model"] = AI_MODEL;
  doc["messages"][0]["role"] = "system";
  doc["messages"][0]["content"] = "Eres un asistente de seguridad inteligente. Ayudas a controlar un sistema de seguridad doméstico.";
  doc["messages"][1]["role"] = "user";
  doc["messages"][1]["content"] = prompt;
  doc["max_tokens"] = 200;
  doc["temperature"] = 0.7;

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);
  String respuesta = "Lo siento, no puedo procesar tu solicitud en este momento.";

  if(httpResponseCode == 200) {
    String response = http.getString();
    DynamicJsonDocument responseDoc(2048);
    deserializeJson(responseDoc, response);

    if(responseDoc["choices"][0]["message"]["content"]) {
      respuesta = responseDoc["choices"][0]["message"]["content"].as<String>();

      // Procesar comandos específicos
      if(mensaje.indexOf("abrir puerta") >= 0 || mensaje.indexOf("abre la puerta") >= 0) {
        // Aquí podrías agregar lógica para abrir la puerta con comando de voz
        respuesta += "\n\n⚠️ Para abrir la puerta necesitas el código maestro en la interfaz web.";
      }

      if(mensaje.indexOf("estado") >= 0 || mensaje.indexOf("cómo está") >= 0) {
        respuesta += "\n\n📊 Estado actual: " + estadoToString(estado_actual);
      }
    }
  }

  http.end();
  return respuesta;
}

void agregarUsuario(String nombre, String rfid_id, String huella_id) {
  Usuario nuevo_usuario;
  nuevo_usuario.nombre = nombre;
  nuevo_usuario.rfid_id = rfid_id;
  nuevo_usuario.huella_id = huella_id;
  nuevo_usuario.activo = true;
  nuevo_usuario.permisos = "basico";
  nuevo_usuario.ultimo_acceso = 0;

  usuarios.push_back(nuevo_usuario);
  guardarUsuarios();

  Serial.println("👤 Usuario agregado: " + nombre);
}

void cargarUsuarios() {
  // Cargar usuarios desde SPIFFS
  File file = SPIFFS.open("/usuarios.json", "r");
  if(file) {
    String contenido = file.readString();
    file.close();

    DynamicJsonDocument doc(2048);
    deserializeJson(doc, contenido);

    usuarios.clear();
    for(JsonObject usuario_obj : doc["usuarios"].as<JsonArray>()) {
      Usuario usuario;
      usuario.nombre = usuario_obj["nombre"].as<String>();
      usuario.rfid_id = usuario_obj["rfid_id"].as<String>();
      usuario.huella_id = usuario_obj["huella_id"].as<String>();
      usuario.activo = usuario_obj["activo"];
      usuario.permisos = usuario_obj["permisos"].as<String>();
      usuario.ultimo_acceso = usuario_obj["ultimo_acceso"];
      usuarios.push_back(usuario);
    }

    Serial.println("👥 Usuarios cargados: " + String(usuarios.size()));
  } else {
    // Crear usuarios por defecto
    agregarUsuario("Admin", "12345678", "001");
    agregarUsuario("Usuario1", "87654321", "002");
    Serial.println("👥 Usuarios por defecto creados");
  }
}

void guardarUsuarios() {
  DynamicJsonDocument doc(2048);

  for(int i = 0; i < usuarios.size(); i++) {
    doc["usuarios"][i]["nombre"] = usuarios[i].nombre;
    doc["usuarios"][i]["rfid_id"] = usuarios[i].rfid_id;
    doc["usuarios"][i]["huella_id"] = usuarios[i].huella_id;
    doc["usuarios"][i]["activo"] = usuarios[i].activo;
    doc["usuarios"][i]["permisos"] = usuarios[i].permisos;
    doc["usuarios"][i]["ultimo_acceso"] = usuarios[i].ultimo_acceso;
  }

  File file = SPIFFS.open("/usuarios.json", "w");
  if(file) {
    serializeJson(doc, file);
    file.close();
    Serial.println("💾 Usuarios guardados");
  }
}

int contarEventosHoy() {
  // Contar eventos del día actual
  unsigned long ahora = millis();
  unsigned long inicio_dia = ahora - (ahora % 86400000); // Inicio del día en ms

  int contador = 0;
  for(const auto& evento : eventos) {
    if(evento.timestamp >= inicio_dia) {
      contador++;
    }
  }
  return contador;
}

String estadoToString(EstadoSistema estado) {
  switch(estado) {
    case ESPERANDO: return "Esperando";
    case DETECTANDO_PROXIMIDAD: return "Detectando proximidad";
    case ESPERANDO_IDENTIFICACION: return "Esperando identificación";
    case PROCESANDO_ACCESO: return "Procesando acceso";
    case ACCESO_CONCEDIDO: return "Acceso concedido";
    case ACCESO_DENEGADO: return "Acceso denegado";
    case MODO_ALERTA: return "Modo alerta";
    default: return "Desconocido";
  }
}

String obtenerTiempoActual() {
  time_t now;
  struct tm timeinfo;
  time(&now);
  localtime_r(&now, &timeinfo);

  char buffer[64];
  strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", &timeinfo);
  return String(buffer);
}

void inicializarESP32CAM() {
  // Verificar conexión con ESP32-CAM
  HTTPClient http;
  http.begin("http://" + String(ESP32CAM_IP) + "/status");

  int httpResponseCode = http.GET();
  if(httpResponseCode == 200) {
    Serial.println("📷 ESP32-CAM conectada");
  } else {
    Serial.println("❌ ESP32-CAM no disponible");
  }

  http.end();
}

void notificarEventoWeb(String tipo_evento) {
  // Aquí puedes agregar notificaciones push, emails, etc.
  Serial.println("📢 Evento notificado: " + tipo_evento);
}

void enviarAlertaCompleta() {
  // Enviar alerta por todos los canales disponibles
  Serial.println("🚨 ALERTA COMPLETA ENVIADA");

  // Aquí puedes agregar:
  // - Envío de emails
  // - Notificaciones push
  // - Mensajes a Telegram
  // - Llamadas automáticas
  // - etc.
}

void enviarAlertaEmergencia() {
  Serial.println("🆘 ALERTA DE EMERGENCIA");

  // Alerta máxima - todos los canales
  enviarAlertaCompleta();

  // Aquí agregar protocolos de emergencia específicos
}

// ===== FUNCIONES PARA NODOS ESP8266 =====

struct NodoESP8266 {
  String node_id;
  String location;
  String ip;
  bool activo;
  bool pir_detectado;
  bool puerta_abierta;
  float temperatura;
  float humedad;
  bool boton_panico;
  unsigned long ultimo_heartbeat;
  int wifi_rssi;
};

std::vector<NodoESP8266> nodos_esp8266;

void procesarDatosNodoESP8266(String datos) {
  DynamicJsonDocument doc(512);
  deserializeJson(doc, datos);

  String node_id = doc["node_id"];
  String location = doc["location"];

  // Buscar nodo existente o crear nuevo
  int indice_nodo = -1;
  for(int i = 0; i < nodos_esp8266.size(); i++) {
    if(nodos_esp8266[i].node_id == node_id) {
      indice_nodo = i;
      break;
    }
  }

  if(indice_nodo == -1) {
    // Crear nuevo nodo
    NodoESP8266 nuevo_nodo;
    nuevo_nodo.node_id = node_id;
    nuevo_nodo.location = location;
    nodos_esp8266.push_back(nuevo_nodo);
    indice_nodo = nodos_esp8266.size() - 1;
    Serial.println("📡 Nuevo nodo ESP8266 registrado: " + node_id);
  }

  // Actualizar datos del nodo
  nodos_esp8266[indice_nodo].activo = doc["estado"]["activo"];
  nodos_esp8266[indice_nodo].pir_detectado = doc["sensores"]["pir"];
  nodos_esp8266[indice_nodo].puerta_abierta = doc["sensores"]["puerta"];
  nodos_esp8266[indice_nodo].temperatura = doc["sensores"]["temperatura"];
  nodos_esp8266[indice_nodo].humedad = doc["sensores"]["humedad"];
  nodos_esp8266[indice_nodo].boton_panico = doc["sensores"]["boton_panico"];
  nodos_esp8266[indice_nodo].wifi_rssi = doc["estado"]["wifi_rssi"];
  nodos_esp8266[indice_nodo].ultimo_heartbeat = millis();

  // Debug
  Serial.println("📊 Datos nodo " + node_id + ": PIR=" + String(nodos_esp8266[indice_nodo].pir_detectado) +
                 " Puerta=" + String(nodos_esp8266[indice_nodo].puerta_abierta) +
                 " Temp=" + String(nodos_esp8266[indice_nodo].temperatura) + "°C");
}

void procesarAlertaNodoESP8266(String datos) {
  DynamicJsonDocument doc(256);
  deserializeJson(doc, datos);

  String node_id = doc["node_id"];
  String location = doc["location"];
  String tipo_evento = doc["tipo_evento"];
  String prioridad = doc["prioridad"];

  Serial.println("🚨 ALERTA de nodo " + node_id + " (" + location + "): " + tipo_evento);

  // Registrar evento de alerta
  EventoAcceso evento_nodo;
  evento_nodo.timestamp = millis();
  evento_nodo.usuario = "NODO_" + node_id;
  evento_nodo.metodo = tipo_evento;
  evento_nodo.exitoso = false;
  evento_nodo.foto_url = "nodo_remoto";
  evento_nodo.analisis_ia = "Alerta de nodo remoto: " + tipo_evento;
  eventos.push_back(evento_nodo);

  // Activar alerta según prioridad
  if(prioridad == "ALTA") {
    estado_actual = MODO_ALERTA;

    // Tomar foto con ESP32-CAM
    String foto_url = solicitarFotoESP32CAM();

    // Consultar IA sobre la alerta del nodo
    String prompt = "Alerta de nodo remoto: " + tipo_evento + " en " + location + ". Evalúa la situación.";
    String analisis_ia = consultarIA("NODO_REMOTO", foto_url, false);

    Serial.println("🤖 IA analiza alerta de nodo: " + analisis_ia);
  }

  // Notificar evento
  notificarEventoWeb("alerta_nodo_" + node_id);
}

void procesarHeartbeatNodo(String datos) {
  DynamicJsonDocument doc(128);
  deserializeJson(doc, datos);

  String node_id = doc["node_id"];

  // Actualizar último heartbeat
  for(int i = 0; i < nodos_esp8266.size(); i++) {
    if(nodos_esp8266[i].node_id == node_id) {
      nodos_esp8266[i].ultimo_heartbeat = millis();
      break;
    }
  }
}

void registrarNuevoNodo(String datos) {
  DynamicJsonDocument doc(256);
  deserializeJson(doc, datos);

  String node_id = doc["node_id"];
  String location = doc["location"];
  String ip = doc["ip"];
  String tipo = doc["tipo"];

  Serial.println("📡 Registrando nuevo nodo: " + node_id + " (" + location + ") IP: " + ip);

  // Verificar si ya existe
  bool existe = false;
  for(const auto& nodo : nodos_esp8266) {
    if(nodo.node_id == node_id) {
      existe = true;
      break;
    }
  }

  if(!existe) {
    NodoESP8266 nuevo_nodo;
    nuevo_nodo.node_id = node_id;
    nuevo_nodo.location = location;
    nuevo_nodo.ip = ip;
    nuevo_nodo.activo = true;
    nuevo_nodo.ultimo_heartbeat = millis();
    nodos_esp8266.push_back(nuevo_nodo);

    Serial.println("✅ Nodo " + node_id + " registrado exitosamente");
  }
}

void verificarNodosESP8266() {
  // Verificar que todos los nodos estén enviando heartbeat
  unsigned long ahora = millis();

  for(int i = 0; i < nodos_esp8266.size(); i++) {
    if(ahora - nodos_esp8266[i].ultimo_heartbeat > 60000) { // 1 minuto sin heartbeat
      if(nodos_esp8266[i].activo) {
        Serial.println("⚠️ Nodo " + nodos_esp8266[i].node_id + " sin respuesta");
        nodos_esp8266[i].activo = false;

        // Registrar evento de nodo desconectado
        EventoAcceso evento;
        evento.timestamp = millis();
        evento.usuario = "NODO_" + nodos_esp8266[i].node_id;
        evento.metodo = "DESCONEXION";
        evento.exitoso = false;
        evento.foto_url = "";
        evento.analisis_ia = "Nodo remoto desconectado";
        eventos.push_back(evento);
      }
    }
  }
}
