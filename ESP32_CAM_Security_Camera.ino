/*
 * 📷 ESP32-CAM - CÁMARA DE SEGURIDAD INTELIGENTE
 * Sistema de Seguridad con IA
 * 
 * Funcionalidades:
 * - Streaming de video en tiempo real
 * - Captura de fotos bajo demanda
 * - Detección de movimiento
 * - Almacenamiento en SD
 * - Comunicación con ESP32 maestro
 * - Servidor web para visualización
 */

#include "esp_camera.h"
#include <WiFi.h>
#include <WebServer.h>
#include <FS.h>
#include <SD_MMC.h>
#include <ArduinoJson.h>
#include <HTTPClient.h>

// ===== CONFIGURACIÓN WiFi =====
const char* ssid = "Redmi Note 12 5G";        // ⚠️ MISMO WiFi que ESP32 maestro
const char* password = "123456789";            // ⚠️ MISMO password

// ===== CONFIGURACIÓN ESP32 MAESTRO =====
const char* ESP32_MASTER_IP = "*************"; // IP del ESP32 maestro
const int ESP32_MASTER_PORT = 80;

// ===== CONFIGURACIÓN CÁMARA (AI-Thinker) =====
#define PWDN_GPIO_NUM     32
#define RESET_GPIO_NUM    -1
#define XCLK_GPIO_NUM      0
#define SIOD_GPIO_NUM     26
#define SIOC_GPIO_NUM     27
#define Y9_GPIO_NUM       35
#define Y8_GPIO_NUM       34
#define Y7_GPIO_NUM       39
#define Y6_GPIO_NUM       36
#define Y5_GPIO_NUM       21
#define Y4_GPIO_NUM       19
#define Y3_GPIO_NUM       18
#define Y2_GPIO_NUM        5
#define VSYNC_GPIO_NUM    25
#define HREF_GPIO_NUM     23
#define PCLK_GPIO_NUM     22

// ===== CONFIGURACIÓN SISTEMA =====
#define LED_FLASH 4        // LED flash integrado
#define PIR_PIN 13         // Sensor PIR para detección de movimiento (opcional)

WebServer server(80);

// ===== VARIABLES GLOBALES =====
bool sistema_activo = true;
bool deteccion_movimiento = false;
bool streaming_activo = false;
int fotos_capturadas = 0;
unsigned long ultima_foto = 0;
unsigned long ultimo_movimiento = 0;

// Buffer para comparación de frames (detección de movimiento)
camera_fb_t * frame_anterior = NULL;

void setup() {
  Serial.begin(115200);
  Serial.println("\n📷 ESP32-CAM Sistema de Seguridad");
  Serial.println("==================================");
  
  // Configurar pines
  configurarPines();
  
  // Inicializar cámara
  if(inicializarCamara()) {
    Serial.println("✅ Cámara inicializada correctamente");
  } else {
    Serial.println("❌ Error inicializando cámara");
    return;
  }
  
  // Conectar WiFi
  conectarWiFi();
  
  // Inicializar SD
  inicializarSD();
  
  // Configurar servidor web
  configurarServidorWeb();
  
  // Notificar al ESP32 maestro
  notificarESP32Maestro("ONLINE");
  
  Serial.println("✅ ESP32-CAM listo");
  Serial.print("🌐 IP: ");
  Serial.println(WiFi.localIP());
  Serial.println("📷 Stream: http://" + WiFi.localIP().toString() + "/stream");
  Serial.println("==================================");
  
  // Señal de inicio
  parpadearFlash(3);
}

void loop() {
  // Manejar servidor web
  server.handleClient();
  
  // Verificar conexión WiFi
  if(WiFi.status() != WL_CONNECTED) {
    Serial.println("❌ WiFi desconectado, reintentando...");
    conectarWiFi();
  }
  
  // Detección de movimiento (si está habilitada)
  if(sistema_activo && deteccion_movimiento) {
    verificarMovimiento();
  }
  
  // Heartbeat con ESP32 maestro cada 30 segundos
  static unsigned long ultimo_heartbeat = 0;
  if(millis() - ultimo_heartbeat > 30000) {
    notificarESP32Maestro("HEARTBEAT");
    ultimo_heartbeat = millis();
  }
  
  delay(100);
}

void configurarPines() {
  pinMode(LED_FLASH, OUTPUT);
  digitalWrite(LED_FLASH, LOW);
  
  // Si tienes sensor PIR conectado
  #ifdef PIR_PIN
  pinMode(PIR_PIN, INPUT);
  #endif
  
  Serial.println("🔧 Pines configurados");
}

bool inicializarCamara() {
  camera_config_t config;
  config.ledc_channel = LEDC_CHANNEL_0;
  config.ledc_timer = LEDC_TIMER_0;
  config.pin_d0 = Y2_GPIO_NUM;
  config.pin_d1 = Y3_GPIO_NUM;
  config.pin_d2 = Y4_GPIO_NUM;
  config.pin_d3 = Y5_GPIO_NUM;
  config.pin_d4 = Y6_GPIO_NUM;
  config.pin_d5 = Y7_GPIO_NUM;
  config.pin_d6 = Y8_GPIO_NUM;
  config.pin_d7 = Y9_GPIO_NUM;
  config.pin_xclk = XCLK_GPIO_NUM;
  config.pin_pclk = PCLK_GPIO_NUM;
  config.pin_vsync = VSYNC_GPIO_NUM;
  config.pin_href = HREF_GPIO_NUM;
  config.pin_sscb_sda = SIOD_GPIO_NUM;
  config.pin_sscb_scl = SIOC_GPIO_NUM;
  config.pin_pwdn = PWDN_GPIO_NUM;
  config.pin_reset = RESET_GPIO_NUM;
  config.xclk_freq_hz = 20000000;
  config.pixel_format = PIXFORMAT_JPEG;
  
  // Configuración de calidad según PSRAM
  if(psramFound()) {
    config.frame_size = FRAMESIZE_UXGA;
    config.jpeg_quality = 10;
    config.fb_count = 2;
    Serial.println("📊 PSRAM encontrada - Alta calidad");
  } else {
    config.frame_size = FRAMESIZE_SVGA;
    config.jpeg_quality = 12;
    config.fb_count = 1;
    Serial.println("📊 Sin PSRAM - Calidad media");
  }
  
  // Inicializar cámara
  esp_err_t err = esp_camera_init(&config);
  if(err != ESP_OK) {
    Serial.printf("❌ Error cámara: 0x%x", err);
    return false;
  }
  
  // Configuraciones adicionales
  sensor_t * s = esp_camera_sensor_get();
  s->set_brightness(s, 0);     // -2 a 2
  s->set_contrast(s, 0);       // -2 a 2
  s->set_saturation(s, 0);     // -2 a 2
  s->set_special_effect(s, 0); // 0 a 6 (0=Normal)
  s->set_whitebal(s, 1);       // 0 = disable , 1 = enable
  s->set_awb_gain(s, 1);       // 0 = disable , 1 = enable
  s->set_wb_mode(s, 0);        // 0 a 4
  s->set_exposure_ctrl(s, 1);  // 0 = disable , 1 = enable
  s->set_aec2(s, 0);           // 0 = disable , 1 = enable
  s->set_ae_level(s, 0);       // -2 a 2
  s->set_aec_value(s, 300);    // 0 a 1200
  s->set_gain_ctrl(s, 1);      // 0 = disable , 1 = enable
  s->set_agc_gain(s, 0);       // 0 a 30
  s->set_gainceiling(s, (gainceiling_t)0);  // 0 a 6
  s->set_bpc(s, 0);            // 0 = disable , 1 = enable
  s->set_wpc(s, 1);            // 0 = disable , 1 = enable
  s->set_raw_gma(s, 1);        // 0 = disable , 1 = enable
  s->set_lenc(s, 1);           // 0 = disable , 1 = enable
  s->set_hmirror(s, 0);        // 0 = disable , 1 = enable
  s->set_vflip(s, 0);          // 0 = disable , 1 = enable
  s->set_dcw(s, 1);            // 0 = disable , 1 = enable
  s->set_colorbar(s, 0);       // 0 = disable , 1 = enable
  
  return true;
}

void conectarWiFi() {
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);
  
  Serial.print("🔄 Conectando a WiFi");
  int intentos = 0;
  while(WiFi.status() != WL_CONNECTED && intentos < 20) {
    delay(500);
    Serial.print(".");
    intentos++;
  }
  
  if(WiFi.status() == WL_CONNECTED) {
    Serial.println("\n✅ WiFi conectado");
    Serial.print("📡 IP: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("\n❌ Error conectando WiFi");
  }
}

void inicializarSD() {
  if(!SD_MMC.begin()) {
    Serial.println("⚠️ SD no disponible");
    return;
  }
  
  uint8_t cardType = SD_MMC.cardType();
  if(cardType == CARD_NONE) {
    Serial.println("⚠️ No hay tarjeta SD");
    return;
  }
  
  Serial.println("💾 Tarjeta SD inicializada");
  Serial.printf("💾 Tamaño: %lluMB\n", SD_MMC.cardSize() / (1024 * 1024));
  
  // Crear directorio para fotos si no existe
  if(!SD_MMC.exists("/fotos")) {
    SD_MMC.mkdir("/fotos");
    Serial.println("📁 Directorio /fotos creado");
  }
}

void configurarServidorWeb() {
  // Página principal
  server.on("/", HTTP_GET, [](){
    server.send(200, "text/html", generarPaginaWeb());
  });
  
  // Stream de video
  server.on("/stream", HTTP_GET, handleStream);
  
  // Capturar foto
  server.on("/capture", HTTP_GET, handleCapture);
  
  // Estado del sistema
  server.on("/status", HTTP_GET, [](){
    DynamicJsonDocument doc(512);
    doc["status"] = "online";
    doc["fotos_capturadas"] = fotos_capturadas;
    doc["streaming_activo"] = streaming_activo;
    doc["deteccion_movimiento"] = deteccion_movimiento;
    doc["sistema_activo"] = sistema_activo;
    doc["memoria_libre"] = ESP.getFreeHeap();
    doc["uptime"] = millis();
    
    String json;
    serializeJson(doc, json);
    server.send(200, "application/json", json);
  });
  
  // Control de sistema
  server.on("/control", HTTP_POST, [](){
    if(server.hasArg("accion")) {
      String accion = server.arg("accion");
      
      if(accion == "activar") {
        sistema_activo = true;
        server.send(200, "text/plain", "Sistema activado");
      }
      else if(accion == "desactivar") {
        sistema_activo = false;
        server.send(200, "text/plain", "Sistema desactivado");
      }
      else if(accion == "flash_on") {
        digitalWrite(LED_FLASH, HIGH);
        server.send(200, "text/plain", "Flash encendido");
      }
      else if(accion == "flash_off") {
        digitalWrite(LED_FLASH, LOW);
        server.send(200, "text/plain", "Flash apagado");
      }
      else if(accion == "toggle_deteccion") {
        deteccion_movimiento = !deteccion_movimiento;
        server.send(200, "text/plain", deteccion_movimiento ? "Detección ON" : "Detección OFF");
      }
    }
  });
  
  // Listar fotos
  server.on("/fotos", HTTP_GET, [](){
    File root = SD_MMC.open("/fotos");
    String html = "<h2>📷 Fotos Capturadas</h2>";
    
    if(root) {
      File file = root.openNextFile();
      while(file) {
        if(!file.isDirectory()) {
          html += "<p><a href='/foto/" + String(file.name()) + "'>" + String(file.name()) + "</a> (" + String(file.size()) + " bytes)</p>";
        }
        file = root.openNextFile();
      }
      root.close();
    }
    
    server.send(200, "text/html", html);
  });
  
  server.begin();
  Serial.println("🌐 Servidor web iniciado");
}

void handleStream() {
  WiFiClient client = server.client();
  
  client.println("HTTP/1.1 200 OK");
  client.println("Content-Type: multipart/x-mixed-replace; boundary=frame");
  client.println();
  
  streaming_activo = true;
  
  while(client.connected()) {
    camera_fb_t * fb = esp_camera_fb_get();
    if(!fb) {
      Serial.println("❌ Error capturando frame");
      break;
    }
    
    client.println("--frame");
    client.println("Content-Type: image/jpeg");
    client.println("Content-Length: " + String(fb->len));
    client.println();
    client.write(fb->buf, fb->len);
    client.println();
    
    esp_camera_fb_return(fb);
    
    if(!client.connected()) break;
    delay(30); // ~30 FPS
  }
  
  streaming_activo = false;
}

void handleCapture() {
  camera_fb_t * fb = esp_camera_fb_get();
  if(!fb) {
    server.send(500, "text/plain", "Error capturando foto");
    return;
  }
  
  // Guardar en SD si está disponible
  String filename = "/fotos/foto_" + String(millis()) + ".jpg";
  if(SD_MMC.exists("/fotos")) {
    File file = SD_MMC.open(filename, FILE_WRITE);
    if(file) {
      file.write(fb->buf, fb->len);
      file.close();
      fotos_capturadas++;
      Serial.println("📷 Foto guardada: " + filename);
    }
  }
  
  // Enviar foto como respuesta HTTP
  server.sendHeader("Content-Disposition", "inline; filename=capture.jpg");
  server.send_P(200, "image/jpeg", (const char *)fb->buf, fb->len);
  
  esp_camera_fb_return(fb);
  
  // Flash de confirmación
  parpadearFlash(1);
  
  ultima_foto = millis();
}

void verificarMovimiento() {
  static unsigned long ultima_verificacion = 0;
  if(millis() - ultima_verificacion < 1000) return; // Verificar cada segundo

  camera_fb_t * fb = esp_camera_fb_get();
  if(!fb) return;

  bool movimiento_detectado = false;

  if(frame_anterior != NULL) {
    // Comparar frames para detectar movimiento
    movimiento_detectado = compararFrames(frame_anterior, fb);
  }

  // Guardar frame actual para próxima comparación
  if(frame_anterior) {
    esp_camera_fb_return(frame_anterior);
  }
  frame_anterior = fb;

  if(movimiento_detectado) {
    Serial.println("🚶 Movimiento detectado!");
    ultimo_movimiento = millis();

    // Capturar foto del movimiento
    capturarFotoMovimiento();

    // Notificar al ESP32 maestro
    notificarESP32Maestro("MOVIMIENTO_DETECTADO");

    // Flash de alerta
    parpadearFlash(2);
  }

  ultima_verificacion = millis();
}

bool compararFrames(camera_fb_t * frame1, camera_fb_t * frame2) {
  // Algoritmo simple de detección de movimiento
  // Compara píxeles cada N posiciones para optimizar

  if(!frame1 || !frame2) return false;
  if(frame1->len != frame2->len) return false;

  int diferencias = 0;
  int umbral_diferencia = 30; // Sensibilidad
  int paso = 100; // Comparar cada 100 píxeles para optimizar

  for(int i = 0; i < frame1->len && i < frame2->len; i += paso) {
    int diff = abs(frame1->buf[i] - frame2->buf[i]);
    if(diff > umbral_diferencia) {
      diferencias++;
    }
  }

  // Si más del 5% de píxeles comparados son diferentes, hay movimiento
  int total_comparaciones = frame1->len / paso;
  float porcentaje_cambio = (float)diferencias / total_comparaciones * 100;

  return porcentaje_cambio > 5.0;
}

void capturarFotoMovimiento() {
  camera_fb_t * fb = esp_camera_fb_get();
  if(!fb) return;

  String filename = "/fotos/movimiento_" + String(millis()) + ".jpg";

  if(SD_MMC.exists("/fotos")) {
    File file = SD_MMC.open(filename, FILE_WRITE);
    if(file) {
      file.write(fb->buf, fb->len);
      file.close();
      fotos_capturadas++;
      Serial.println("📷 Foto de movimiento guardada: " + filename);
    }
  }

  esp_camera_fb_return(fb);
}

void notificarESP32Maestro(String evento) {
  HTTPClient http;
  http.begin("http://" + String(ESP32_MASTER_IP) + "/api/evento-camara");
  http.addHeader("Content-Type", "application/json");

  DynamicJsonDocument doc(256);
  doc["evento"] = evento;
  doc["timestamp"] = millis();
  doc["ip_camara"] = WiFi.localIP().toString();
  doc["fotos_disponibles"] = fotos_capturadas;

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);

  if(httpResponseCode > 0) {
    Serial.println("📡 Evento enviado al maestro: " + evento);
  } else {
    Serial.println("❌ Error enviando evento: " + String(httpResponseCode));
  }

  http.end();
}

String generarPaginaWeb() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<title>📷 ESP32-CAM Seguridad</title>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<style>";
  html += "body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #1a1a1a; color: white; }";
  html += ".container { max-width: 1000px; margin: 0 auto; }";
  html += ".header { text-align: center; margin-bottom: 30px; }";
  html += ".video-container { text-align: center; margin: 20px 0; }";
  html += ".video-stream { max-width: 100%; border: 2px solid #333; border-radius: 10px; }";
  html += ".controls { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }";
  html += ".control-card { background: #333; padding: 20px; border-radius: 10px; }";
  html += ".btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }";
  html += ".btn:hover { background: #0056b3; }";
  html += ".btn-danger { background: #dc3545; }";
  html += ".btn-success { background: #28a745; }";
  html += ".btn-warning { background: #ffc107; color: black; }";
  html += ".status { background: #2a2a2a; padding: 15px; border-radius: 10px; margin: 10px 0; }";
  html += ".status-online { color: #28a745; }";
  html += ".status-offline { color: #dc3545; }";
  html += "</style>";
  html += "<script>";
  html += "function actualizarEstado() {";
  html += "  fetch('/status').then(r => r.json()).then(data => {";
  html += "    document.getElementById('fotos-count').innerHTML = data.fotos_capturadas;";
  html += "    document.getElementById('streaming-status').innerHTML = data.streaming_activo ? 'Activo' : 'Inactivo';";
  html += "    document.getElementById('deteccion-status').innerHTML = data.deteccion_movimiento ? 'Activada' : 'Desactivada';";
  html += "    document.getElementById('sistema-status').innerHTML = data.sistema_activo ? 'Activo' : 'Inactivo';";
  html += "    document.getElementById('memoria-libre').innerHTML = Math.round(data.memoria_libre / 1024) + ' KB';";
  html += "    document.getElementById('uptime').innerHTML = Math.round(data.uptime / 1000) + ' seg';";
  html += "  });";
  html += "}";
  html += "function enviarComando(accion) {";
  html += "  fetch('/control', { method: 'POST', headers: {'Content-Type': 'application/x-www-form-urlencoded'}, body: 'accion=' + accion })";
  html += "  .then(r => r.text()).then(data => { alert(data); actualizarEstado(); });";
  html += "}";
  html += "function capturarFoto() {";
  html += "  window.open('/capture', '_blank');";
  html += "}";
  html += "setInterval(actualizarEstado, 2000);";
  html += "window.onload = actualizarEstado;";
  html += "</script>";
  html += "</head><body>";

  html += "<div class='container'>";
  html += "<div class='header'>";
  html += "<h1>📷 ESP32-CAM Sistema de Seguridad</h1>";
  html += "<p>Vigilancia Inteligente con IA</p>";
  html += "</div>";

  // Video stream
  html += "<div class='video-container'>";
  html += "<h3>🎥 Stream en Vivo</h3>";
  html += "<img src='/stream' class='video-stream' alt='Video Stream'>";
  html += "</div>";

  // Controles
  html += "<div class='controls'>";

  // Estado del sistema
  html += "<div class='control-card'>";
  html += "<h4>📊 Estado del Sistema</h4>";
  html += "<div class='status'>";
  html += "<p>Sistema: <span id='sistema-status' class='status-online'>Activo</span></p>";
  html += "<p>Streaming: <span id='streaming-status'>Inactivo</span></p>";
  html += "<p>Detección: <span id='deteccion-status'>Desactivada</span></p>";
  html += "<p>Fotos: <span id='fotos-count'>0</span></p>";
  html += "<p>Memoria: <span id='memoria-libre'>0 KB</span></p>";
  html += "<p>Uptime: <span id='uptime'>0 seg</span></p>";
  html += "</div>";
  html += "</div>";

  // Controles de cámara
  html += "<div class='control-card'>";
  html += "<h4>📷 Controles de Cámara</h4>";
  html += "<button class='btn btn-success' onclick='capturarFoto()'>📸 Capturar Foto</button>";
  html += "<button class='btn btn-warning' onclick='enviarComando(\"flash_on\")'>💡 Flash ON</button>";
  html += "<button class='btn' onclick='enviarComando(\"flash_off\")'>💡 Flash OFF</button>";
  html += "<button class='btn' onclick='window.open(\"/fotos\", \"_blank\")'>📁 Ver Fotos</button>";
  html += "</div>";

  // Controles de sistema
  html += "<div class='control-card'>";
  html += "<h4>⚙️ Controles de Sistema</h4>";
  html += "<button class='btn btn-success' onclick='enviarComando(\"activar\")'>✅ Activar Sistema</button>";
  html += "<button class='btn btn-danger' onclick='enviarComando(\"desactivar\")'>❌ Desactivar Sistema</button>";
  html += "<button class='btn btn-warning' onclick='enviarComando(\"toggle_deteccion\")'>🚶 Toggle Detección</button>";
  html += "</div>";

  html += "</div>";

  // Footer
  html += "<div style='text-align: center; margin-top: 30px; color: #666;'>";
  html += "<p>ESP32-CAM Security System | IP: " + WiFi.localIP().toString() + "</p>";
  html += "</div>";

  html += "</div>";
  html += "</body></html>";

  return html;
}

void parpadearFlash(int veces) {
  for(int i = 0; i < veces; i++) {
    digitalWrite(LED_FLASH, HIGH);
    delay(100);
    digitalWrite(LED_FLASH, LOW);
    delay(100);
  }
}
