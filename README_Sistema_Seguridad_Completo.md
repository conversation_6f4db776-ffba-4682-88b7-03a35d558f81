# 🏠 Sistema de Seguridad Inteligente con IA

**El sistema de seguridad más completo para tu hogar** - Combina múltiples sensores, cámara de vigilancia e inteligencia artificial en un solo proyecto.

## 🚀 **¿Qué hace este sistema?**

### **🔐 Control de Acceso Multi-Factor**
- **RFID RC522**: Tarjetas de acceso
- **Lector de Huella**: Identificación biométrica
- **Sensores IR**: Detección de proximidad
- **HC-SR04**: Confirmación de presencia

### **📷 Vigilancia Inteligente**
- **ESP32-CAM**: Stream en vivo + fotos automáticas
- **Detección de movimiento**: Algoritmo de comparación de frames
- **Almacenamiento**: Fotos en tarjeta SD
- **Flash automático**: Para fotos nocturnas

### **🤖 Inteligencia Artificial**
- **Análisis de eventos**: IA decide si permitir/denegar acceso
- **Chat inteligente**: Control por voz/texto
- **Detección de patrones**: Aprende comportamientos normales
- **Alertas contextuales**: Reduce falsas alarmas

### **📱 Interfaz Web Completa**
- **Dashboard en tiempo real**: Estado de todos los sensores
- **Control remoto**: Abrir puertas, armar/desarmar sistema
- **Galería de fotos**: Ver capturas de seguridad
- **Chat con IA**: Comandos de voz y consultas
- **Gestión de usuarios**: Agregar/eliminar accesos

## 🔧 **Hardware Necesario**

### **ESP32 (Cerebro del Sistema)**
- ESP32 DevKit v1 o similar
- Buzzer 5V
- 3x LEDs (Verde, Rojo, Azul)
- Relé 5V (para cerradura)
- Botón de pánico
- Resistencias 220Ω

### **Arduino UNO (Hub de Sensores)**
- Arduino UNO R3
- RFID RC522
- Lector de huella dactilar
- HC-SR04 (sensor ultrasónico)
- 2x Sensores IR de proximidad
- Buzzer
- LED de estado

### **ESP32-CAM (Vigilancia)**
- ESP32-CAM AI-Thinker
- Tarjeta microSD (16GB+)
- Antena WiFi externa (opcional)

### **Accesorios**
- Protoboards y cables jumper
- Fuente de alimentación 5V/3A
- Caja/gabinete para montaje
- Tarjetas RFID

## 🔌 **Conexiones Detalladas**

### **ESP32 Maestro**
```
ESP32          Componente
GPIO2    →     Buzzer (+)
GPIO4    →     LED Verde (+)
GPIO5    →     LED Rojo (+)
GPIO18   →     LED Azul (+)
GPIO19   →     Relé (IN)
GPIO21   →     Botón Pánico
GPIO16   →     Arduino RX (Serial2)
GPIO17   →     Arduino TX (Serial2)
GND      →     Todos los GND
5V       →     Alimentación componentes
```

### **Arduino UNO**
```
Arduino        Componente
D2       →     Sensor IR 1
D3       →     Sensor IR 2
D4       →     Huella RX
D5       →     Huella TX
D6       →     HC-SR04 Echo
D7       →     HC-SR04 Trig
D8       →     Buzzer
D9       →     RFID RST
D10      →     RFID SS
D11      →     RFID MOSI
D12      →     RFID MISO
D13      →     RFID SCK + LED Status
```

### **ESP32-CAM**
```
ESP32-CAM      Componente
GPIO4    →     LED Flash
GPIO13   →     Sensor PIR (opcional)
5V       →     Alimentación
GND      →     GND
```

## ⚙️ **Instalación Paso a Paso**

### **1. Preparar Arduino IDE**
```bash
# Instalar librerías necesarias:
- ArduinoJson (6.21.3+)
- MFRC522 (1.4.10+)
- ESPAsyncWebServer
- AsyncTCP
- ESP32 Camera (esp32-camera)
```

### **2. Configurar APIs**
Edita cada archivo .ino y cambia:
```cpp
// WiFi (MISMO en todos los dispositivos)
const char* ssid = "TU_WIFI_AQUI";
const char* password = "TU_PASSWORD_AQUI";

// API de IA (solo en ESP32 maestro)
const char* AI_API_URL = "https://api.openai.com/v1/chat/completions";
const char* AI_API_KEY = "tu-api-key-aqui";
```

### **3. Subir Códigos**
1. **Arduino UNO**: `Arduino_UNO_Sensors_Hub.ino`
2. **ESP32 Maestro**: `ESP32_Master_Security_System.ino`
3. **ESP32-CAM**: `ESP32_CAM_Security_Camera.ino`

### **4. Configurar IPs Fijas**
En tu router, asigna IPs fijas:
- ESP32 Maestro: `*************`
- ESP32-CAM: `*************`

## 🎮 **Cómo Usar el Sistema**

### **🌐 Acceso Web**
1. Conecta a tu WiFi
2. Ve a `http://*************` (ESP32 maestro)
3. Dashboard completo con todos los controles

### **📱 Funciones Principales**

#### **Control de Acceso**
1. **Acércate** → Sensores IR detectan proximidad
2. **Presenta tarjeta RFID** o **pon el dedo** en lector
3. **IA analiza** → Decide permitir/denegar
4. **ESP32-CAM toma foto** → Registro visual
5. **Puerta se abre** (si es autorizado)

#### **Chat con IA**
```
"¿Quién entró hoy?"
"Activa modo seguridad"
"Muéstrame las últimas fotos"
"¿Está todo normal?"
```

#### **Vigilancia**
- **Stream en vivo**: `http://*************/stream`
- **Fotos automáticas** cuando hay movimiento
- **Almacenamiento** en tarjeta SD
- **Notificaciones** al sistema maestro

## 🤖 **Integración con IA**

### **Casos de Uso**
1. **Análisis de Acceso**
   ```
   Usuario: Juan presenta RFID
   IA: "Usuario autorizado, horario normal, permitir acceso"
   Acción: Abrir puerta + foto de confirmación
   ```

2. **Detección de Anomalías**
   ```
   Evento: Movimiento a las 3 AM
   IA: "Horario inusual, no hay acceso programado"
   Acción: ALERTA MÁXIMA + foto + notificación
   ```

3. **Control por Voz**
   ```
   Usuario: "IA, ¿quién entró hoy?"
   IA: "Hoy entraron: Juan (8:30 AM), María (2:15 PM)"
   ```

### **Configurar tu API de IA**

#### **OpenAI (GPT)**
```cpp
const char* AI_API_URL = "https://api.openai.com/v1/chat/completions";
const char* AI_API_KEY = "sk-tu-api-key-aqui";
const char* AI_MODEL = "gpt-3.5-turbo";
```

#### **Claude (Anthropic)**
```cpp
const char* AI_API_URL = "https://api.anthropic.com/v1/messages";
const char* AI_API_KEY = "tu-claude-key-aqui";
const char* AI_MODEL = "claude-3-sonnet-20240229";
```

#### **API Personalizada**
```cpp
const char* AI_API_URL = "https://tu-api.com/analyze";
const char* AI_API_KEY = "tu-key-personalizada";
```

## 📊 **Funcionalidades Avanzadas**

### **🚨 Sistema de Alertas**
- **Nivel 1**: Acceso denegado → Foto + log
- **Nivel 2**: Movimiento no autorizado → Alerta + grabación
- **Nivel 3**: Botón de pánico → ALERTA MÁXIMA
- **Nivel 4**: Intento de forzar → Todos los protocolos

### **📈 Dashboard Inteligente**
- **Estado en tiempo real** de todos los sensores
- **Gráficos de acceso** por horas/días
- **Predicciones de IA** sobre patrones
- **Recomendaciones** de seguridad

### **👥 Gestión de Usuarios**
- **Agregar usuarios** con RFID + huella
- **Permisos granulares** (horarios, días)
- **Historial de accesos** por usuario
- **Activar/desactivar** usuarios temporalmente

## 🔧 **Personalización**

### **Cambiar Sensibilidad**
```cpp
// En Arduino_UNO_Sensors_Hub.ino
const unsigned long DEBOUNCE_DELAY = 50;  // Sensores IR
int umbral_diferencia = 30;               // Detección movimiento
```

### **Modificar Timeouts**
```cpp
// En ESP32_Master_Security_System.ino
const unsigned long TIMEOUT_PUERTA = 10000;  // 10 segundos
const unsigned long COOLDOWN_IA = 2000;      // 2 segundos entre consultas IA
```

### **Personalizar Respuestas IA**
```cpp
String prompt = "Eres un asistente de seguridad. Analiza este evento...";
// Modifica el prompt según tus necesidades
```

## 🚨 **Solución de Problemas**

### **❌ ESP32 no se conecta a WiFi**
- Verifica SSID y password
- Asegúrate de usar red 2.4GHz
- Revisa alcance de señal

### **❌ Arduino no responde**
- Verifica conexiones Serial (RX/TX)
- Comprueba alimentación 5V
- Revisa baudrate (9600)

### **❌ RFID no lee tarjetas**
- Verifica conexiones SPI
- Comprueba alimentación 3.3V
- Acerca más la tarjeta

### **❌ ESP32-CAM no toma fotos**
- Verifica tarjeta SD
- Comprueba alimentación estable
- Revisa configuración de cámara

### **❌ IA no responde**
- Verifica API key
- Comprueba conexión a internet
- Revisa formato de request

## 🎯 **Próximas Mejoras**

- **App móvil** nativa (Android/iOS)
- **Reconocimiento facial** avanzado
- **Integración con Alexa/Google**
- **Notificaciones push** en tiempo real
- **Backup en la nube** automático
- **Control por Telegram**
- **Integración con sistemas domóticos**

## 📄 **Licencia**

Este proyecto es open source bajo licencia MIT. ¡Úsalo, modifícalo y compártelo!

---

## 🏆 **¡Felicidades!**

¡Has creado el sistema de seguridad más avanzado para el hogar! 🎉

**Características únicas:**
- ✅ Control multi-factor (RFID + Huella + IR + Ultrasónico)
- ✅ Vigilancia inteligente con ESP32-CAM
- ✅ IA integrada para análisis y control
- ✅ Interfaz web completa y moderna
- ✅ Sistema de alertas multinivel
- ✅ Almacenamiento local y en la nube
- ✅ Control remoto total

**¡Tu hogar nunca fue tan seguro e inteligente!** 🏠🔒🤖
