#ifndef CONFIG_H
#define CONFIG_H

// ===== CONFIGURACIÓN DE RED =====
// IMPORTANTE: Copia este archivo a include/config.h y modifica los valores

// WiFi origen (al que se conectará el ESP8266)
#define WIFI_SSID_ORIGEN "MI_WIFI_CASA"           // Cambia por tu WiFi
#define WIFI_PASSWORD_ORIGEN "mi_password_wifi"    // Cambia por tu password

// Punto de acceso (hotspot que creará el ESP8266)
#define AP_SSID "ESP8266_Hotspot"                 // Nombre del hotspot
#define AP_PASSWORD "12345678"                    // Password del hotspot (mínimo 8 caracteres)

// ===== CONFIGURACIÓN DE LÍMITES =====
#define LIMITE_MB 100           // Límite en MB por cliente (ajusta según necesites)
#define MAX_CLIENTES 4          // Máximo número de clientes simultáneos

// ===== CONFIGURACIÓN DE MONITOREO =====
#define INTERVALO_VERIFICACION 5000  // Intervalo de verificación en ms (5 segundos)
#define AUTO_REFRESH_WEB 10000       // Auto-refresh de la página web en ms (10 segundos)

// ===== CONFIGURACIÓN DE RED AP =====
#define AP_IP_ADDRESS 192, 168, 4, 1  // IP del punto de acceso
#define AP_GATEWAY 192, 168, 4, 1     // Gateway del punto de acceso
#define AP_SUBNET 255, 255, 255, 0    // Máscara de subred

// ===== CONFIGURACIÓN DE SERVIDOR WEB =====
#define WEB_SERVER_PORT 80

// ===== CONFIGURACIÓN DE DEBUG =====
#define SERIAL_BAUD_RATE 115200
#define DEBUG_ENABLED true

// ===== CONFIGURACIÓN AVANZADA =====
#define EEPROM_SIZE 512
#define WIFI_CONNECT_TIMEOUT 20  // Timeout en segundos para conexión WiFi
#define CLIENT_TIMEOUT 300000    // Timeout para clientes inactivos (5 minutos)

// ===== SIMULACIÓN DE DATOS =====
// Para demostración, se simula el uso de datos
// En una implementación real, esto se obtendría del tráfico de red real
#define SIMULATE_DATA_USAGE true
#define MIN_BYTES_PER_CHECK 1024    // Mínimo bytes simulados por verificación (1 KB)
#define MAX_BYTES_PER_CHECK 8192    // Máximo bytes simulados por verificación (8 KB)

// ===== EJEMPLOS DE CONFIGURACIÓN =====

// Para uso doméstico ligero:
// #define LIMITE_MB 50
// #define MAX_CLIENTES 2

// Para uso comercial:
// #define LIMITE_MB 500
// #define MAX_CLIENTES 10

// Para pruebas rápidas (límite bajo):
// #define LIMITE_MB 1
// #define MIN_BYTES_PER_CHECK 50000
// #define MAX_BYTES_PER_CHECK 100000

#endif // CONFIG_H
