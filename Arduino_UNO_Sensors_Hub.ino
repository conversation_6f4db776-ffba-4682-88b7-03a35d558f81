/*
 * 🔧 ARDUINO UNO - HUB DE SENSORES
 * Sistema de Seguridad Inteligente
 * 
 * Sensores conectados:
 * - RFID RC522 (SPI)
 * - <PERSON><PERSON> de <PERSON> (Serial)
 * - HC-SR04 Ultrasónico
 * - 2x Sensores IR de proximidad
 * 
 * Comunicación:
 * - Serial con ESP32 (comandos JSON)
 * - Envía datos de todos los sensores
 * - Recibe comandos de control
 */

#include <SPI.h>
#include <MFRC522.h>
#include <SoftwareSerial.h>
#include <ArduinoJson.h>

// ===== PINES RFID RC522 =====
#define RST_PIN 9
#define SS_PIN 10
MFRC522 mfrc522(SS_PIN, RST_PIN);

// ===== PINES HC-SR04 =====
#define TRIG_PIN 7
#define ECHO_PIN 6

// ===== PINES SENSORES IR =====
#define IR1_PIN 2  // Sensor IR 1 (entrada)
#define IR2_PIN 3  // Sensor IR 2 (salida)

// ===== LECTOR DE HUELLA (Serial) =====
SoftwareSerial huellaSerial(4, 5); // RX, TX

// ===== PINES INDICADORES =====
#define LED_STATUS 13
#define BUZZER_PIN 8

// ===== VARIABLES GLOBALES =====
struct SensorData {
  bool ir1_detectado;
  bool ir2_detectado;
  int distancia_ultrasonico;
  String rfid_detectado;
  bool huella_detectada;
  String huella_id;
  unsigned long timestamp;
};

SensorData sensores;
unsigned long ultima_lectura = 0;
const unsigned long INTERVALO_LECTURA = 100; // 100ms

// Variables para debounce de sensores IR
bool ir1_estado_anterior = false;
bool ir2_estado_anterior = false;
unsigned long ir1_ultimo_cambio = 0;
unsigned long ir2_ultimo_cambio = 0;
const unsigned long DEBOUNCE_DELAY = 50;

void setup() {
  Serial.begin(9600);
  huellaSerial.begin(9600);
  
  Serial.println("🔧 Arduino UNO - Hub de Sensores");
  Serial.println("================================");
  
  // Configurar pines
  configurarPines();
  
  // Inicializar RFID
  SPI.begin();
  mfrc522.PCD_Init();
  Serial.println("📡 RFID RC522 inicializado");
  
  // Inicializar lector de huella
  inicializarLectorHuella();
  
  // Inicializar sensores
  inicializarSensores();
  
  Serial.println("✅ Todos los sensores inicializados");
  Serial.println("📊 Enviando datos cada 100ms");
  Serial.println("================================");
  
  // Señal de inicio
  parpadearLED(3);
}

void loop() {
  // Procesar comandos del ESP32
  procesarComandosSerial();
  
  // Leer sensores periódicamente
  if(millis() - ultima_lectura > INTERVALO_LECTURA) {
    leerTodosSensores();
    enviarDatosESP32();
    ultima_lectura = millis();
  }
  
  // Indicador de actividad
  static unsigned long ultimo_parpadeo = 0;
  if(millis() - ultimo_parpadeo > 1000) {
    digitalWrite(LED_STATUS, !digitalRead(LED_STATUS));
    ultimo_parpadeo = millis();
  }
  
  delay(10);
}

void configurarPines() {
  // Sensores IR
  pinMode(IR1_PIN, INPUT);
  pinMode(IR2_PIN, INPUT);
  
  // HC-SR04
  pinMode(TRIG_PIN, OUTPUT);
  pinMode(ECHO_PIN, INPUT);
  
  // Indicadores
  pinMode(LED_STATUS, OUTPUT);
  pinMode(BUZZER_PIN, OUTPUT);
  
  Serial.println("🔧 Pines configurados");
}

void inicializarLectorHuella() {
  huellaSerial.println("INIT");
  delay(1000);
  
  if(huellaSerial.available()) {
    String respuesta = huellaSerial.readString();
    if(respuesta.indexOf("OK") >= 0) {
      Serial.println("👆 Lector de huella inicializado");
    } else {
      Serial.println("❌ Error inicializando lector de huella");
    }
  } else {
    Serial.println("⚠️ Lector de huella no responde");
  }
}

void inicializarSensores() {
  // Leer estado inicial
  sensores.ir1_detectado = false;
  sensores.ir2_detectado = false;
  sensores.distancia_ultrasonico = 0;
  sensores.rfid_detectado = "";
  sensores.huella_detectada = false;
  sensores.huella_id = "";
  sensores.timestamp = millis();
  
  Serial.println("📊 Sensores inicializados");
}

void procesarComandosSerial() {
  if(Serial.available()) {
    String comando = Serial.readStringUntil('\n');
    comando.trim();
    
    if(comando == "READ_SENSORS") {
      // Comando para leer todos los sensores
      leerTodosSensores();
      enviarDatosESP32();
    }
    else if(comando == "STATUS") {
      enviarEstadoSistema();
    }
    else if(comando == "RESET") {
      reiniciarSensores();
    }
    else if(comando.startsWith("BUZZER:")) {
      int duracion = comando.substring(7).toInt();
      tone(BUZZER_PIN, 1000, duracion);
    }
  }
}

void leerTodosSensores() {
  // Leer sensores IR con debounce
  leerSensoresIR();
  
  // Leer sensor ultrasónico
  leerSensorUltrasonico();
  
  // Leer RFID
  leerRFID();
  
  // Leer lector de huella
  leerLectorHuella();
  
  // Actualizar timestamp
  sensores.timestamp = millis();
}

void leerSensoresIR() {
  // Leer IR1 con debounce
  bool ir1_actual = digitalRead(IR1_PIN);
  if(ir1_actual != ir1_estado_anterior) {
    ir1_ultimo_cambio = millis();
  }
  if((millis() - ir1_ultimo_cambio) > DEBOUNCE_DELAY) {
    sensores.ir1_detectado = ir1_actual;
  }
  ir1_estado_anterior = ir1_actual;
  
  // Leer IR2 con debounce
  bool ir2_actual = digitalRead(IR2_PIN);
  if(ir2_actual != ir2_estado_anterior) {
    ir2_ultimo_cambio = millis();
  }
  if((millis() - ir2_ultimo_cambio) > DEBOUNCE_DELAY) {
    sensores.ir2_detectado = ir2_actual;
  }
  ir2_estado_anterior = ir2_actual;
}

void leerSensorUltrasonico() {
  // Enviar pulso
  digitalWrite(TRIG_PIN, LOW);
  delayMicroseconds(2);
  digitalWrite(TRIG_PIN, HIGH);
  delayMicroseconds(10);
  digitalWrite(TRIG_PIN, LOW);
  
  // Leer eco
  long duracion = pulseIn(ECHO_PIN, HIGH, 30000); // Timeout 30ms
  
  if(duracion > 0) {
    sensores.distancia_ultrasonico = duracion * 0.034 / 2; // Convertir a cm
  } else {
    sensores.distancia_ultrasonico = 999; // Error o fuera de rango
  }
  
  // Limitar rango válido
  if(sensores.distancia_ultrasonico > 400) {
    sensores.distancia_ultrasonico = 400;
  }
}

void leerRFID() {
  sensores.rfid_detectado = ""; // Reset
  
  // Verificar si hay tarjeta presente
  if(!mfrc522.PICC_IsNewCardPresent()) {
    return;
  }
  
  // Seleccionar tarjeta
  if(!mfrc522.PICC_ReadCardSerial()) {
    return;
  }
  
  // Leer UID
  String uid = "";
  for(byte i = 0; i < mfrc522.uid.size; i++) {
    uid += String(mfrc522.uid.uidByte[i] < 0x10 ? "0" : "");
    uid += String(mfrc522.uid.uidByte[i], HEX);
  }
  uid.toUpperCase();
  
  sensores.rfid_detectado = uid;
  
  // Detener comunicación con tarjeta
  mfrc522.PICC_HaltA();
  mfrc522.PCD_StopCrypto1();
  
  if(uid != "") {
    Serial.println("📡 RFID detectado: " + uid);
    tone(BUZZER_PIN, 1200, 100); // Beep de confirmación
  }
}

void leerLectorHuella() {
  sensores.huella_detectada = false;
  sensores.huella_id = "";
  
  // Enviar comando de lectura al lector de huella
  huellaSerial.println("READ");
  
  // Esperar respuesta (timeout corto para no bloquear)
  unsigned long timeout = millis() + 50;
  String respuesta = "";
  
  while(millis() < timeout && huellaSerial.available()) {
    respuesta += (char)huellaSerial.read();
    delay(1);
  }
  
  if(respuesta.length() > 0) {
    respuesta.trim();
    
    if(respuesta.startsWith("FOUND:")) {
      sensores.huella_detectada = true;
      sensores.huella_id = respuesta.substring(6);
      Serial.println("👆 Huella detectada: " + sensores.huella_id);
      tone(BUZZER_PIN, 1500, 150); // Beep diferente para huella
    }
  }
}

void enviarDatosESP32() {
  // Crear JSON con todos los datos de sensores
  DynamicJsonDocument doc(512);
  
  doc["ir1"] = sensores.ir1_detectado;
  doc["ir2"] = sensores.ir2_detectado;
  doc["distancia"] = sensores.distancia_ultrasonico;
  doc["rfid"] = sensores.rfid_detectado;
  doc["huella"] = sensores.huella_detectada;
  doc["huella_id"] = sensores.huella_id;
  doc["timestamp"] = sensores.timestamp;
  
  // Enviar JSON por Serial
  serializeJson(doc, Serial);
  Serial.println(); // Nueva línea para delimitar
}

void enviarEstadoSistema() {
  DynamicJsonDocument doc(256);
  
  doc["status"] = "online";
  doc["sensores_activos"] = 5;
  doc["uptime"] = millis();
  doc["memoria_libre"] = freeMemory();
  
  serializeJson(doc, Serial);
  Serial.println();
}

void reiniciarSensores() {
  Serial.println("🔄 Reiniciando sensores...");
  
  // Reinicializar RFID
  mfrc522.PCD_Reset();
  mfrc522.PCD_Init();
  
  // Reinicializar lector de huella
  huellaSerial.println("RESET");
  
  // Reset variables
  inicializarSensores();
  
  // Confirmación
  parpadearLED(2);
  tone(BUZZER_PIN, 800, 200);
  
  Serial.println("✅ Sensores reiniciados");
}

void parpadearLED(int veces) {
  for(int i = 0; i < veces; i++) {
    digitalWrite(LED_STATUS, HIGH);
    delay(200);
    digitalWrite(LED_STATUS, LOW);
    delay(200);
  }
}

// Función para calcular memoria libre (aproximada)
int freeMemory() {
  extern int __heap_start, *__brkval;
  int v;
  return (int) &v - (__brkval == 0 ? (int) &__heap_start : (int) __brkval);
}
