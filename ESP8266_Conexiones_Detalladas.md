# 🔌 ESP8266 - Conexiones Detalladas para Nodo Sensor Remoto

## 📋 **Lista de Componentes**

### **ESP8266 NodeMCU v1.0**
- 1x ESP8266 NodeMCU (o Wemos D1 Mini)

### **Sensores**
- 1x **Sensor PIR HC-SR501** (detección de movimiento)
- 1x **Sensor magnético de puerta** (reed switch)
- 1x **DHT22** (temperatura y humedad)
- 1x **Botón pulsador** (pánico)

### **Actuadores**
- 1x **Buzzer activo 5V** (sirena)
- 3x **LEDs** (estado, alarma, WiFi)
- 3x **Resistencias 220Ω** (para LEDs)

### **Accesorios**
- 1x **Protoboard**
- **Cables jumper** macho-macho y macho-hembra
- **Fuente 5V/2A** o alimentación USB

## 🔌 **Diagrama de Conexiones**

### **ESP8266 NodeMCU - Pinout**
```
         ESP8266 NodeMCU
    ┌─────────────────────────┐
    │  [RST]           [A0]   │
    │  [A0]            [D0]   │ ← GPIO16 (LED WiFi)
    │  [D0]            [D1]   │ ← GPIO5 (Botón Pánico)
    │  [D1]            [D2]   │ ← GPIO4 (DHT22)
    │  [D2]            [D3]   │ ← GPIO0 (Sensor Puerta)
    │  [D3]            [D4]   │ ← GPIO2 (PIR)
    │  [D4]            [3V3]  │
    │  [3V3]           [GND]  │
    │  [GND]           [D5]   │ ← GPIO14 (LED Alarma)
    │  [D5]            [D6]   │ ← GPIO12 (Sirena)
    │  [D6]            [D7]   │ ← GPIO13 (LED Estado)
    │  [D7]            [D8]   │
    │  [D8]            [RX]   │
    │  [RX]            [TX]   │
    │  [TX]            [GND]  │
    │  [GND]           [3V3]  │
    │  [3V3]           [VIN]  │ ← 5V entrada
    └─────────────────────────┘
```

## 📡 **Conexiones Detalladas**

### **1. Sensor PIR HC-SR501**
```
PIR HC-SR501    →    ESP8266 NodeMCU
VCC             →    3V3 (o 5V si disponible)
GND             →    GND
OUT             →    D4 (GPIO2)
```

**Configuración PIR:**
- **Sensibilidad**: Ajustar potenciómetro según necesidad
- **Tiempo**: Configurar para 3-5 segundos
- **Modo**: Jumper en posición "H" (retriggering)

### **2. Sensor Magnético de Puerta**
```
Reed Switch     →    ESP8266 NodeMCU
Terminal 1      →    D3 (GPIO0)
Terminal 2      →    GND
```

**Nota**: El ESP8266 tiene pull-up interno activado en GPIO0.

### **3. Sensor DHT22**
```
DHT22           →    ESP8266 NodeMCU
VCC (+)         →    3V3
GND (-)         →    GND
DATA            →    D2 (GPIO4)
```

**Resistencia pull-up**: Agregar resistencia de 10kΩ entre VCC y DATA del DHT22.

### **4. Botón de Pánico**
```
Botón           →    ESP8266 NodeMCU
Terminal 1      →    D1 (GPIO5)
Terminal 2      →    GND
```

**Nota**: Pull-up interno activado en código.

### **5. Sirena/Buzzer**
```
Buzzer 5V       →    ESP8266 NodeMCU
VCC (+)         →    VIN (5V)
GND (-)         →    GND
Control         →    D6 (GPIO12)
```

**Importante**: Si el buzzer consume mucha corriente, usar un transistor o relé.

### **6. LEDs Indicadores**

#### **LED de Estado (Verde)**
```
LED Verde       →    ESP8266 NodeMCU
Ánodo (+)       →    D7 (GPIO13)
Cátodo (-)      →    Resistencia 220Ω → GND
```

#### **LED de Alarma (Rojo)**
```
LED Rojo        →    ESP8266 NodeMCU
Ánodo (+)       →    D5 (GPIO14)
Cátodo (-)      →    Resistencia 220Ω → GND
```

#### **LED WiFi (Azul)**
```
LED Azul        →    ESP8266 NodeMCU
Ánodo (+)       →    D0 (GPIO16)
Cátodo (-)      →    Resistencia 220Ω → GND
```

## ⚡ **Alimentación**

### **Opción 1: USB (Desarrollo)**
- Conectar cable micro-USB al NodeMCU
- Alimentación: 5V/1A mínimo

### **Opción 2: Fuente Externa (Producción)**
- Fuente 5V/2A conectada a pin VIN
- GND de fuente a GND del NodeMCU

### **Opción 3: Batería (Portátil)**
- Power bank USB o batería Li-Po con regulador
- Para uso temporal o ubicaciones sin alimentación

## 🔧 **Esquema de Conexión en Protoboard**

```
Protoboard Layout:

    5V  3V3  GND     ESP8266 NodeMCU     GND  3V3  5V
     │   │    │                           │    │    │
     │   │    ├─────────────────────────────┤    │    │
     │   │    │                           │    │    │
     │   ├────┼─── DHT22 (VCC)            │    │    │
     │   │    │                           │    │    │
     ├───┼────┼─── Buzzer (VCC)           │    │    │
     │   │    │                           │    │    │
     │   │    ├─── PIR (GND)              │    │    │
     │   │    ├─── DHT22 (GND)            │    │    │
     │   │    ├─── Reed Switch (GND)      │    │    │
     │   │    ├─── Botón (GND)            │    │    │
     │   │    ├─── LEDs (Cátodos)         │    │    │
     │   │    └─── Buzzer (GND)           │    │    │

Conexiones de señal:
D4 (GPIO2)  ── PIR (OUT)
D3 (GPIO0)  ── Reed Switch
D2 (GPIO4)  ── DHT22 (DATA)
D1 (GPIO5)  ── Botón Pánico
D6 (GPIO12) ── Buzzer (Control)
D7 (GPIO13) ── LED Estado
D5 (GPIO14) ── LED Alarma
D0 (GPIO16) ── LED WiFi
```

## 🎯 **Ubicaciones Recomendadas**

### **Entrada Secundaria**
- PIR: Esquina superior para cubrir área de entrada
- Sensor puerta: Marco de la puerta (parte fija y móvil)
- Botón pánico: Lugar discreto pero accesible

### **Ventana Importante**
- PIR: Interior, apuntando hacia la ventana
- Sensor puerta: Marco de ventana
- DHT22: Lugar protegido de humedad directa

### **Garaje/Sótano**
- PIR: Entrada principal del área
- Sensores adicionales según necesidad
- Sirena: Lugar audible pero protegido

## 🔍 **Verificación de Conexiones**

### **Checklist Pre-Encendido**
- [ ] Todas las conexiones de GND están unidas
- [ ] VCC de sensores conectado a 3V3 o 5V según especificación
- [ ] No hay cortocircuitos entre VCC y GND
- [ ] LEDs conectados con resistencias limitadoras
- [ ] Polaridad correcta en LEDs y sensores

### **Test Inicial**
1. **Conectar alimentación** → LED WiFi debe parpadear
2. **Verificar Serial Monitor** → Mensajes de inicio
3. **Test de sensores** → Mover mano frente al PIR
4. **Test de puerta** → Abrir/cerrar con imán
5. **Test de botón** → Presionar botón de pánico
6. **Test de sirena** → Usar interfaz web

## 🚨 **Solución de Problemas**

### **PIR no detecta movimiento**
- Verificar alimentación (3V3 o 5V según modelo)
- Ajustar sensibilidad con potenciómetro
- Esperar 1-2 minutos para calibración inicial

### **DHT22 no lee datos**
- Verificar resistencia pull-up de 10kΩ
- Comprobar conexiones VCC, GND, DATA
- Esperar 2 segundos entre lecturas

### **Sensor de puerta siempre activado**
- Verificar que el imán esté cerca del reed switch
- Comprobar que el pull-up interno esté activado
- Test con multímetro: debe mostrar continuidad cuando el imán está cerca

### **WiFi no conecta**
- Verificar SSID y password en código
- Asegurar red 2.4GHz (no 5GHz)
- Verificar alcance de señal WiFi

### **Sirena no suena**
- Verificar alimentación del buzzer (5V)
- Comprobar si necesita transistor para mayor corriente
- Test directo: conectar buzzer a 5V momentáneamente

## 📦 **Lista de Compras**

### **Componentes Básicos**
- 1x ESP8266 NodeMCU v1.0
- 1x Sensor PIR HC-SR501
- 1x Reed switch (sensor magnético)
- 1x DHT22 (AM2302)
- 1x Buzzer activo 5V
- 1x Botón pulsador
- 3x LEDs (verde, rojo, azul)
- 3x Resistencias 220Ω
- 1x Resistencia 10kΩ (pull-up DHT22)

### **Accesorios**
- 1x Protoboard 830 puntos
- 20x Cables jumper macho-macho
- 10x Cables jumper macho-hembra
- 1x Fuente 5V/2A
- 1x Caja/gabinete para montaje

### **Herramientas**
- Multímetro
- Alicates pelacables
- Destornilladores
- Pistola de calor (opcional)

## 💡 **Consejos de Instalación**

1. **Planificación**: Mide distancias y planifica rutas de cables antes de instalar
2. **Alimentación**: Usa fuente estable, evita alimentar desde USB en producción
3. **Protección**: Coloca en caja resistente al agua si es instalación exterior
4. **Alcance WiFi**: Verifica señal WiFi en ubicación final antes de instalar
5. **Mantenimiento**: Deja acceso fácil para actualizaciones y mantenimiento

¡Con estas conexiones tu ESP8266 será un nodo sensor súper completo! 🚀
