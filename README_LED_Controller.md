# 🔥 ESP8266 Controlador de LEDs por WiFi

¡Controla LEDs desde tu celular usando una interfaz web súper fácil!

## 🚀 ¿Qué hace este proyecto?

- **Controla LEDs individuales** desde tu celular/computadora
- **Efectos especiales**: Arcoíris y parpadeo
- **Interfaz web moderna** con botones grandes y fáciles de usar
- **Estado en tiempo real** de todos los LEDs
- **Responsive design** - funciona perfecto en móviles

## 📱 Capturas de Pantalla

La interfaz web incluye:
- 🔴🟢🔵🟡 Controles individuales para cada LED
- 🌈 Efecto arcoíris (secuencia de colores)
- ✨ Efecto parpadeo (todos los LEDs)
- 🌟 Encender/apagar todos a la vez
- 📊 Estado del sistema en tiempo real

## 🔧 Hardware Necesario

### Mínimo (Solo LED integrado):
- **ESP8266** (NodeMCU, Wemos D1 Mini, ESP-01)
- **Cable USB** para programación

### Completo (4 LEDs externos):
- **ESP8266** (NodeMCU, Wemos D1 Mini)
- **4 LEDs** (rojo, verde, azul, amarillo)
- **4 Resistencias** de 220Ω - 1kΩ
- **Protoboard** y cables jumper

## 🔌 Conexiones

### Opción 1: Solo LED Integrado (Más Fácil)
```
ESP8266 NodeMCU/Wemos D1 Mini
- LED integrado en GPIO2 (ya conectado)
- ¡No necesitas conectar nada más!
```

### Opción 2: LEDs Externos (Más Divertido)
```
ESP8266 NodeMCU    →    LED + Resistencia
GPIO2 (D4)         →    LED Rojo + 220Ω → GND
GPIO0 (D3)         →    LED Verde + 220Ω → GND  
GPIO4 (D2)         →    LED Azul + 220Ω → GND
GPIO5 (D1)         →    LED Amarillo + 220Ω → GND
```

### Esquema de Conexión:
```
ESP8266    Resistencia    LED      GND
GPIO2  ----[220Ω]----[LED Rojo]----GND
GPIO0  ----[220Ω]----[LED Verde]---GND
GPIO4  ----[220Ω]----[LED Azul]----GND
GPIO5  ----[220Ω]----[LED Amarillo]-GND
```

## ⚙️ Instalación Súper Fácil

### Paso 1: Configurar Arduino IDE
1. **Instala Arduino IDE** (si no lo tienes)
2. **Agrega ESP8266**:
   - Ve a `Archivo > Preferencias`
   - En "URLs adicionales" agrega: `http://arduino.esp8266.com/stable/package_esp8266com_index.json`
   - Ve a `Herramientas > Placa > Gestor de tarjetas`
   - Busca "ESP8266" e instala

### Paso 2: Configurar el Código
1. **Abre** `LED_Controller_Simple.ino`
2. **Cambia estas líneas** (15-16):
   ```cpp
   const char* ssid = "TU_WIFI_AQUI";        // ⚠️ Pon tu WiFi
   const char* password = "TU_PASSWORD_AQUI"; // ⚠️ Pon tu password
   ```

### Paso 3: Subir el Código
1. **Conecta** tu ESP8266 por USB
2. **Selecciona** tu placa:
   - `Herramientas > Placa > NodeMCU 1.0` (o tu modelo)
3. **Selecciona** el puerto COM correcto
4. **Haz clic** en "Subir" ⬆️

### Paso 4: ¡Usar!
1. **Abre** el Monitor Serie (115200 baud)
2. **Busca** la IP que aparece (ej: *************)
3. **Abre** esa IP en tu navegador
4. **¡Controla tus LEDs!** 🎉

## 📱 Cómo Usar la Interfaz Web

### Controles Individuales:
- **🔴 LED Rojo**: Botones ENCENDER/APAGAR
- **🟢 LED Verde**: Botones ENCENDER/APAGAR  
- **🔵 LED Azul**: Botones ENCENDER/APAGAR
- **🟡 LED Amarillo**: Botones ENCENDER/APAGAR

### Efectos Especiales:
- **🌈 Arcoíris**: Secuencia automática de colores
- **✨ Parpadeo**: Todos los LEDs parpadean juntos
- **🌟 Todos ON**: Enciende todos los LEDs
- **⚫ Todos OFF**: Apaga todos los LEDs

### Indicadores de Estado:
- **Círculos verdes** = LED encendido
- **Círculos rojos** = LED apagado
- **Actualización automática** cada segundo

## 🎯 URLs de Control (API)

Puedes controlar los LEDs directamente con estas URLs:

```
http://TU_IP/led/rojo/on          - Encender LED rojo
http://TU_IP/led/rojo/off         - Apagar LED rojo
http://TU_IP/led/verde/on         - Encender LED verde
http://TU_IP/led/verde/off        - Apagar LED verde
http://TU_IP/led/azul/on          - Encender LED azul
http://TU_IP/led/azul/off         - Apagar LED azul
http://TU_IP/led/amarillo/on      - Encender LED amarillo
http://TU_IP/led/amarillo/off     - Apagar LED amarillo

http://TU_IP/todos/on             - Encender todos
http://TU_IP/todos/off            - Apagar todos
http://TU_IP/efecto/arcoiris      - Toggle efecto arcoíris
http://TU_IP/efecto/parpadeo      - Toggle efecto parpadeo
http://TU_IP/estado               - Estado en JSON
```

## 🔧 Personalización

### Cambiar Pines de LEDs:
```cpp
const int LED_ROJO = 2;      // Cambia por el pin que uses
const int LED_VERDE = 0;     // GPIO que prefieras
const int LED_AZUL = 4;      // Según tu conexión
const int LED_AMARILLO = 5;  // El que tengas disponible
```

### Solo LED Integrado:
```cpp
// Cambia todos los pines al mismo (LED integrado)
const int LED_ROJO = 2;
const int LED_VERDE = 2;
const int LED_AZUL = 2;
const int LED_AMARILLO = 2;
```

### Cambiar Velocidad de Efectos:
```cpp
// En la función efectoArcoiris()
if(millis() - ultimoCambio > 500) { // Cambia 500 por otro valor

// En la función efectoParpadeo()  
if(millis() - ultimoParpadeo > 300) { // Cambia 300 por otro valor
```

## 🐛 Solución de Problemas

### ❌ No se conecta al WiFi
- Verifica SSID y password
- Asegúrate de que sea red 2.4GHz (no 5GHz)
- Revisa que el ESP8266 esté en rango

### ❌ No compila el código
- Verifica que tengas instalado ESP8266 en Arduino IDE
- Selecciona la placa correcta
- Instala las librerías necesarias

### ❌ LEDs no encienden
- Verifica las conexiones
- Comprueba que las resistencias estén bien
- Revisa que los pines en el código coincidan con tu conexión

### ❌ Página web no carga
- Verifica la IP en el Monitor Serie
- Asegúrate de estar en la misma red WiFi
- Prueba refrescar la página

## 🎉 Ideas de Mejoras

- **Más LEDs**: Agrega más colores
- **Sensor de luz**: Enciende automáticamente de noche
- **Control por voz**: Integra con Alexa/Google
- **Notificaciones**: LEDs que parpadeen con mensajes
- **Música**: Sincroniza con ritmo musical
- **Colores RGB**: Usa LEDs RGB para millones de colores

## 📞 Soporte

Si tienes problemas:
1. Revisa el Monitor Serie para errores
2. Verifica todas las conexiones
3. Asegúrate de que el código esté bien configurado
4. Prueba con solo el LED integrado primero

## 🏆 ¡Felicidades!

¡Ya tienes tu propio controlador de LEDs por WiFi! 🎉

Ahora puedes:
- Controlar luces desde cualquier parte de tu casa
- Impresionar a tus amigos con efectos geniales
- Aprender más sobre IoT y programación
- Expandir el proyecto con más funciones

**¡Diviértete creando!** 🚀
