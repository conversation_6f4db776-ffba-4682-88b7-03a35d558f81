# 🚀 Guía de Instalación - ESP8266 WiFi Repeater con Limitador

## 📋 Requisitos Previos

### Hardware Necesario
- **ESP8266** (NodeMCU, Wemos D1 Mini, ESP-01, etc.)
- **Cable USB** para programación
- **Computadora** con Windows/Linux/Mac

### Software Necesario
- **PlatformIO IDE** o **Arduino IDE**
- **Drivers USB** para tu ESP8266

## 🔧 Paso 1: Configuración del Entorno

### Opción A: PlatformIO (Recomendado)
1. Instala **Visual Studio Code**
2. Instala la extensión **PlatformIO IDE**
3. Reinicia VS Code

### Opción B: Arduino IDE
1. Descarga e instala **Arduino IDE**
2. Agrega el board manager de ESP8266:
   - Ve a `Archivo > Preferencias`
   - En "URLs adicionales de gestor de tarjetas" agrega:
     ```
     http://arduino.esp8266.com/stable/package_esp8266com_index.json
     ```
3. Instala el paquete ESP8266:
   - Ve a `Herramientas > Placa > Gestor de tarjetas`
   - Busca "ESP8266" e instala

## ⚙️ Paso 2: Configuración del Proyecto

### 1. Configurar WiFi y Parámetros

Edita el archivo `include/config.h` y modifica estos valores:

```cpp
// ===== CONFIGURACIÓN OBLIGATORIA =====
#define WIFI_SSID_ORIGEN "TU_WIFI_AQUI"        // ⚠️ CAMBIAR
#define WIFI_PASSWORD_ORIGEN "TU_PASSWORD_AQUI" // ⚠️ CAMBIAR

#define AP_SSID "ESP8266_Hotspot"              // Nombre del hotspot
#define AP_PASSWORD "12345678"                 // Password del hotspot

#define LIMITE_MB 100                          // Límite en MB por cliente
#define MAX_CLIENTES 4                         // Máximo clientes simultáneos
```

### 2. Configuraciones Opcionales

```cpp
// Para uso doméstico ligero
#define LIMITE_MB 50
#define MAX_CLIENTES 2

// Para pruebas rápidas (límite muy bajo)
#define LIMITE_MB 1
#define MIN_BYTES_PER_CHECK 50000  // Simula uso rápido de datos
```

## 🔌 Paso 3: Conexión del Hardware

1. **Conecta el ESP8266** a tu computadora vía USB
2. **Verifica el puerto COM** en el administrador de dispositivos
3. **Instala drivers** si es necesario:
   - NodeMCU: Driver CH340 o CP2102
   - Wemos D1 Mini: Driver CH340

## 📤 Paso 4: Compilación y Carga

### Con PlatformIO:
```bash
# Compilar
pio run

# Subir al ESP8266
pio run --target upload

# Ver monitor serie
pio device monitor
```

### Con Arduino IDE:
1. Abre `src/main.cpp`
2. Selecciona tu placa ESP8266
3. Selecciona el puerto COM correcto
4. Haz clic en "Subir"

## 🌐 Paso 5: Verificación y Uso

### 1. Monitor Serie
Después de cargar el código, abre el monitor serie (115200 baud) para ver:
```
=== ESP8266 WiFi Repeater con Limitador ===
Conectando a WiFi origen: TU_WIFI_AQUI
WiFi origen conectado!
IP asignada: *************
Configurando punto de acceso: ESP8266_Hotspot
Punto de acceso iniciado correctamente
IP del AP: ***********
Servidor web iniciado en puerto 80
Sistema iniciado correctamente
```

### 2. Conexión de Dispositivos
1. **Busca la red WiFi** "ESP8266_Hotspot" (o el nombre que configuraste)
2. **Conéctate** usando la password configurada
3. **Abre un navegador** y ve a: `http://***********`

### 3. Interfaz Web
Deberías ver una página con:
- ✅ Estado de conexiones
- 📱 Lista de clientes conectados
- 📊 Uso de datos en tiempo real
- ⚠️ Alertas de límites

## 🔍 Solución de Problemas

### ❌ No se conecta al WiFi origen
```
Error: No se pudo conectar al WiFi origen
```
**Solución:**
- Verifica SSID y password en `config.h`
- Asegúrate de que el WiFi esté en rango
- Revisa que no sea una red de 5GHz (ESP8266 solo soporta 2.4GHz)

### ❌ No se puede cargar el código
```
Error: espcomm_upload_mem failed
```
**Solución:**
- Verifica el puerto COM
- Instala drivers USB
- Presiona el botón RESET en el ESP8266
- Intenta con velocidad de carga más baja

### ❌ Página web no carga
**Solución:**
- Verifica que estés conectado al hotspot del ESP8266
- Usa la IP exacta: `http://***********`
- Revisa el monitor serie para errores

### ❌ Clientes no pueden conectarse
**Solución:**
- Verifica que el password tenga al menos 8 caracteres
- Comprueba que no se haya alcanzado el límite de clientes
- Reinicia el ESP8266

## 📊 Monitoreo y Estadísticas

### URLs Disponibles:
- `http://***********/` - Interfaz principal
- `http://***********/stats` - Estadísticas JSON
- `http://***********/reset` - Reiniciar estadísticas
- `http://***********/limite` - Página de límite excedido

### Comandos Serie:
El monitor serie muestra información en tiempo real:
```
Cliente *********** - Datos usados: 1024 KB / 100 MB
¡LÍMITE EXCEDIDO! Cliente *********** ha usado 100 MB de 100 MB permitidos
Desconectando cliente por límite excedido: ***********
```

## 🎯 Configuraciones Recomendadas

### Para Casa (Uso Familiar):
```cpp
#define LIMITE_MB 200
#define MAX_CLIENTES 6
#define INTERVALO_VERIFICACION 10000  // 10 segundos
```

### Para Negocio (Control Estricto):
```cpp
#define LIMITE_MB 50
#define MAX_CLIENTES 10
#define INTERVALO_VERIFICACION 3000   // 3 segundos
```

### Para Pruebas (Límite Rápido):
```cpp
#define LIMITE_MB 1
#define MIN_BYTES_PER_CHECK 100000    // 100KB por verificación
#define INTERVALO_VERIFICACION 2000   // 2 segundos
```

## 🔄 Actualizaciones

Para actualizar el código:
1. Modifica los archivos necesarios
2. Recompila y sube nuevamente
3. Las estadísticas se mantendrán en EEPROM (opcional)

## 📞 Soporte

Si tienes problemas:
1. Revisa el monitor serie para errores específicos
2. Verifica todas las configuraciones en `config.h`
3. Asegúrate de que el hardware esté funcionando correctamente
4. Prueba con configuraciones más simples primero

¡Tu ESP8266 WiFi Repeater con Limitador está listo para usar! 🎉
