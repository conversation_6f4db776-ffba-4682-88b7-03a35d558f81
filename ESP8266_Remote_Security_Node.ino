/*
 * 🏠 ESP8266 - NODO SENSOR REMOTO PARA SISTEMA DE SEGURIDAD
 * 
 * FUNCIONES:
 * - Sensor de puerta/ventana (magnético)
 * - Sensor de movimiento PIR
 * - Sensor de temperatura/humedad DHT22
 * - Botón de pánico remoto
 * - Sirena/alarma externa
 * - LEDs indicadores de estado
 * - Comunicación WiFi con ESP32 maestro
 * - Interfaz web local para configuración
 * 
 * UBICACIÓN SUGERIDA:
 * - Entrada secundaria
 * - Ventanas importantes
 * - Garaje o sótano
 * - Habitaciones críticas
 */

#include <ESP8266WiFi.h>
#include <ESP8266HTTPClient.h>
#include <ESP8266WebServer.h>
#include <ArduinoJson.h>
#include <DHT.h>

// ===== CONFIGURACIÓN WiFi =====
const char* ssid = "Redmi Note 12 5G";        // ⚠️ MISMO WiFi que ESP32 maestro
const char* password = "123456789";            // ⚠️ MISMO password

// ===== CONFIGURACIÓN ESP32 MAESTRO =====
const char* ESP32_MASTER_IP = "*************"; // IP del ESP32 maestro
const int ESP32_MASTER_PORT = 80;

// ===== CONFIGURACIÓN DEL NODO =====
const char* NODE_ID = "ESP8266_NODO_01";       // ID único de este nodo
const char* NODE_LOCATION = "Entrada_Trasera";  // Ubicación del nodo

// ===== PINES ESP8266 =====
const int PIN_PIR = 2;           // GPIO2 - Sensor PIR (D4 en NodeMCU)
const int PIN_DOOR_SENSOR = 0;   // GPIO0 - Sensor magnético puerta (D3)
const int PIN_DHT22 = 4;         // GPIO4 - Sensor DHT22 (D2)
const int PIN_PANIC_BUTTON = 5;  // GPIO5 - Botón pánico (D1)
const int PIN_SIRENA = 12;       // GPIO12 - Sirena/Buzzer (D6)
const int PIN_LED_STATUS = 13;   // GPIO13 - LED estado (D7)
const int PIN_LED_ALARM = 14;    // GPIO14 - LED alarma (D5)
const int PIN_LED_WIFI = 16;     // GPIO16 - LED WiFi (D0)

// ===== CONFIGURACIÓN DHT22 =====
#define DHT_TYPE DHT22
DHT dht(PIN_DHT22, DHT_TYPE);

// ===== SERVIDOR WEB LOCAL =====
ESP8266WebServer server(80);

// ===== VARIABLES GLOBALES =====
struct SensorData {
  bool pir_detectado;
  bool puerta_abierta;
  float temperatura;
  float humedad;
  bool boton_panico;
  bool sistema_activo;
  unsigned long timestamp;
};

SensorData sensores;
bool nodo_activo = true;
bool modo_alarma = false;
bool wifi_conectado = false;

// Variables para debounce
bool pir_estado_anterior = false;
bool puerta_estado_anterior = false;
bool panic_estado_anterior = false;
unsigned long ultimo_cambio_pir = 0;
unsigned long ultimo_cambio_puerta = 0;
unsigned long ultimo_cambio_panic = 0;
const unsigned long DEBOUNCE_DELAY = 100;

// Variables de comunicación
unsigned long ultimo_envio_datos = 0;
unsigned long ultimo_heartbeat = 0;
const unsigned long INTERVALO_DATOS = 2000;     // Enviar datos cada 2 segundos
const unsigned long INTERVALO_HEARTBEAT = 30000; // Heartbeat cada 30 segundos

// Variables de alarma
unsigned long inicio_alarma = 0;
const unsigned long DURACION_ALARMA = 10000;    // Alarma por 10 segundos

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("\n🏠 ESP8266 - Nodo Sensor Remoto");
  Serial.println("ID: " + String(NODE_ID));
  Serial.println("Ubicación: " + String(NODE_LOCATION));
  Serial.println("================================");
  
  // Configurar pines
  configurarPines();
  
  // Inicializar DHT22
  dht.begin();
  Serial.println("🌡️ DHT22 inicializado");
  
  // Conectar WiFi
  conectarWiFi();
  
  // Configurar servidor web local
  configurarServidorWeb();
  
  // Inicializar sensores
  inicializarSensores();
  
  // Registrar nodo con ESP32 maestro
  registrarNodoMaestro();
  
  Serial.println("✅ Nodo sensor listo");
  Serial.print("🌐 IP local: ");
  Serial.println(WiFi.localIP());
  Serial.println("================================");
  
  // Señal de inicio
  secuenciaInicioLEDs();
}

void loop() {
  // Manejar servidor web local
  server.handleClient();
  
  // Verificar conexión WiFi
  verificarWiFi();
  
  // Leer sensores
  leerSensores();
  
  // Enviar datos al ESP32 maestro
  if(millis() - ultimo_envio_datos > INTERVALO_DATOS) {
    enviarDatosESP32();
    ultimo_envio_datos = millis();
  }
  
  // Heartbeat con ESP32 maestro
  if(millis() - ultimo_heartbeat > INTERVALO_HEARTBEAT) {
    enviarHeartbeat();
    ultimo_heartbeat = millis();
  }
  
  // Procesar alarma si está activa
  if(modo_alarma) {
    procesarAlarma();
  }
  
  // Actualizar LEDs de estado
  actualizarLEDsEstado();
  
  delay(50);
}

void configurarPines() {
  // Sensores (INPUT)
  pinMode(PIN_PIR, INPUT);
  pinMode(PIN_DOOR_SENSOR, INPUT_PULLUP);  // Pull-up para sensor magnético
  pinMode(PIN_PANIC_BUTTON, INPUT_PULLUP); // Pull-up para botón
  
  // Actuadores (OUTPUT)
  pinMode(PIN_SIRENA, OUTPUT);
  pinMode(PIN_LED_STATUS, OUTPUT);
  pinMode(PIN_LED_ALARM, OUTPUT);
  pinMode(PIN_LED_WIFI, OUTPUT);
  
  // Estado inicial
  digitalWrite(PIN_SIRENA, LOW);
  digitalWrite(PIN_LED_STATUS, LOW);
  digitalWrite(PIN_LED_ALARM, LOW);
  digitalWrite(PIN_LED_WIFI, LOW);
  
  Serial.println("🔧 Pines configurados");
}

void conectarWiFi() {
  WiFi.mode(WIFI_STA);
  WiFi.begin(ssid, password);
  
  Serial.print("🔄 Conectando a WiFi");
  int intentos = 0;
  while(WiFi.status() != WL_CONNECTED && intentos < 30) {
    delay(500);
    Serial.print(".");
    digitalWrite(PIN_LED_WIFI, !digitalRead(PIN_LED_WIFI)); // Parpadeo durante conexión
    intentos++;
  }
  
  if(WiFi.status() == WL_CONNECTED) {
    wifi_conectado = true;
    digitalWrite(PIN_LED_WIFI, HIGH);
    Serial.println("\n✅ WiFi conectado");
    Serial.print("📡 IP: ");
    Serial.println(WiFi.localIP());
  } else {
    wifi_conectado = false;
    digitalWrite(PIN_LED_WIFI, LOW);
    Serial.println("\n❌ Error conectando WiFi");
  }
}

void verificarWiFi() {
  if(WiFi.status() != WL_CONNECTED) {
    if(wifi_conectado) {
      Serial.println("❌ WiFi desconectado");
      wifi_conectado = false;
      digitalWrite(PIN_LED_WIFI, LOW);
    }
    
    // Intentar reconectar cada 30 segundos
    static unsigned long ultimo_intento = 0;
    if(millis() - ultimo_intento > 30000) {
      Serial.println("🔄 Reintentando conexión WiFi...");
      conectarWiFi();
      ultimo_intento = millis();
    }
  } else if(!wifi_conectado) {
    wifi_conectado = true;
    digitalWrite(PIN_LED_WIFI, HIGH);
    Serial.println("✅ WiFi reconectado");
  }
}

void inicializarSensores() {
  sensores.pir_detectado = false;
  sensores.puerta_abierta = false;
  sensores.temperatura = 0.0;
  sensores.humedad = 0.0;
  sensores.boton_panico = false;
  sensores.sistema_activo = true;
  sensores.timestamp = millis();
  
  Serial.println("📊 Sensores inicializados");
}

void leerSensores() {
  // Leer PIR con debounce
  bool pir_actual = digitalRead(PIN_PIR);
  if(pir_actual != pir_estado_anterior) {
    ultimo_cambio_pir = millis();
  }
  if((millis() - ultimo_cambio_pir) > DEBOUNCE_DELAY) {
    if(sensores.pir_detectado != pir_actual) {
      sensores.pir_detectado = pir_actual;
      if(pir_actual) {
        Serial.println("🚶 PIR: Movimiento detectado!");
        if(nodo_activo) {
          activarAlarma("MOVIMIENTO_PIR");
        }
      }
    }
  }
  pir_estado_anterior = pir_actual;
  
  // Leer sensor de puerta con debounce
  bool puerta_actual = !digitalRead(PIN_DOOR_SENSOR); // Invertido (LOW = cerrada)
  if(puerta_actual != puerta_estado_anterior) {
    ultimo_cambio_puerta = millis();
  }
  if((millis() - ultimo_cambio_puerta) > DEBOUNCE_DELAY) {
    if(sensores.puerta_abierta != puerta_actual) {
      sensores.puerta_abierta = puerta_actual;
      Serial.println("🚪 Puerta: " + String(puerta_actual ? "ABIERTA" : "CERRADA"));
      if(puerta_actual && nodo_activo) {
        activarAlarma("PUERTA_ABIERTA");
      }
    }
  }
  puerta_estado_anterior = puerta_actual;
  
  // Leer botón de pánico con debounce
  bool panic_actual = !digitalRead(PIN_PANIC_BUTTON); // Invertido (LOW = no presionado)
  if(panic_actual != panic_estado_anterior) {
    ultimo_cambio_panic = millis();
  }
  if((millis() - ultimo_cambio_panic) > DEBOUNCE_DELAY) {
    if(sensores.boton_panico != panic_actual) {
      sensores.boton_panico = panic_actual;
      if(panic_actual) {
        Serial.println("🆘 BOTÓN DE PÁNICO ACTIVADO!");
        activarAlarma("BOTON_PANICO");
      }
    }
  }
  panic_estado_anterior = panic_actual;
  
  // Leer DHT22 cada 5 segundos
  static unsigned long ultima_lectura_dht = 0;
  if(millis() - ultima_lectura_dht > 5000) {
    float temp = dht.readTemperature();
    float hum = dht.readHumidity();
    
    if(!isnan(temp) && !isnan(hum)) {
      sensores.temperatura = temp;
      sensores.humedad = hum;
    }
    ultima_lectura_dht = millis();
  }
  
  // Actualizar timestamp
  sensores.timestamp = millis();
}

void activarAlarma(String tipo_evento) {
  if(!modo_alarma) {
    modo_alarma = true;
    inicio_alarma = millis();
    
    Serial.println("🚨 ALARMA ACTIVADA: " + tipo_evento);
    
    // Enviar alerta inmediata al ESP32 maestro
    enviarAlertaESP32(tipo_evento);
    
    // Activar sirena
    digitalWrite(PIN_SIRENA, HIGH);
    digitalWrite(PIN_LED_ALARM, HIGH);
  }
}

void procesarAlarma() {
  // Parpadear LED de alarma
  static unsigned long ultimo_parpadeo = 0;
  if(millis() - ultimo_parpadeo > 200) {
    digitalWrite(PIN_LED_ALARM, !digitalRead(PIN_LED_ALARM));
    ultimo_parpadeo = millis();
  }
  
  // Desactivar alarma después del tiempo configurado
  if(millis() - inicio_alarma > DURACION_ALARMA) {
    modo_alarma = false;
    digitalWrite(PIN_SIRENA, LOW);
    digitalWrite(PIN_LED_ALARM, LOW);
    Serial.println("⏰ Alarma desactivada por timeout");
  }
}

void enviarDatosESP32() {
  if(!wifi_conectado) return;

  HTTPClient http;
  WiFiClient client;

  http.begin(client, "http://" + String(ESP32_MASTER_IP) + "/api/nodo-sensor");
  http.addHeader("Content-Type", "application/json");

  // Crear JSON con datos del nodo
  DynamicJsonDocument doc(512);
  doc["node_id"] = NODE_ID;
  doc["location"] = NODE_LOCATION;
  doc["timestamp"] = sensores.timestamp;
  doc["sensores"]["pir"] = sensores.pir_detectado;
  doc["sensores"]["puerta"] = sensores.puerta_abierta;
  doc["sensores"]["temperatura"] = sensores.temperatura;
  doc["sensores"]["humedad"] = sensores.humedad;
  doc["sensores"]["boton_panico"] = sensores.boton_panico;
  doc["estado"]["activo"] = nodo_activo;
  doc["estado"]["alarma"] = modo_alarma;
  doc["estado"]["wifi_rssi"] = WiFi.RSSI();
  doc["estado"]["memoria_libre"] = ESP.getFreeHeap();

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);

  if(httpResponseCode > 0) {
    // Procesar respuesta del ESP32 maestro
    String response = http.getString();
    procesarRespuestaESP32(response);
  } else {
    Serial.println("❌ Error enviando datos: " + String(httpResponseCode));
  }

  http.end();
}

void enviarAlertaESP32(String tipo_evento) {
  if(!wifi_conectado) return;

  HTTPClient http;
  WiFiClient client;

  http.begin(client, "http://" + String(ESP32_MASTER_IP) + "/api/alerta-nodo");
  http.addHeader("Content-Type", "application/json");

  DynamicJsonDocument doc(256);
  doc["node_id"] = NODE_ID;
  doc["location"] = NODE_LOCATION;
  doc["tipo_evento"] = tipo_evento;
  doc["timestamp"] = millis();
  doc["prioridad"] = "ALTA";

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);

  if(httpResponseCode == 200) {
    Serial.println("📡 Alerta enviada al ESP32 maestro");
  } else {
    Serial.println("❌ Error enviando alerta: " + String(httpResponseCode));
  }

  http.end();
}

void enviarHeartbeat() {
  if(!wifi_conectado) return;

  HTTPClient http;
  WiFiClient client;

  http.begin(client, "http://" + String(ESP32_MASTER_IP) + "/api/heartbeat");
  http.addHeader("Content-Type", "application/json");

  DynamicJsonDocument doc(128);
  doc["node_id"] = NODE_ID;
  doc["status"] = "online";
  doc["uptime"] = millis();

  String jsonString;
  serializeJson(doc, jsonString);

  http.POST(jsonString);
  http.end();
}

void procesarRespuestaESP32(String response) {
  DynamicJsonDocument doc(256);
  deserializeJson(doc, response);

  // Procesar comandos del ESP32 maestro
  if(doc["comando"]) {
    String comando = doc["comando"];

    if(comando == "ACTIVAR_NODO") {
      nodo_activo = true;
      Serial.println("✅ Nodo activado por ESP32 maestro");
    }
    else if(comando == "DESACTIVAR_NODO") {
      nodo_activo = false;
      modo_alarma = false;
      digitalWrite(PIN_SIRENA, LOW);
      digitalWrite(PIN_LED_ALARM, LOW);
      Serial.println("❌ Nodo desactivado por ESP32 maestro");
    }
    else if(comando == "ACTIVAR_ALARMA") {
      activarAlarma("COMANDO_REMOTO");
    }
    else if(comando == "DESACTIVAR_ALARMA") {
      modo_alarma = false;
      digitalWrite(PIN_SIRENA, LOW);
      digitalWrite(PIN_LED_ALARM, LOW);
      Serial.println("⏰ Alarma desactivada por comando remoto");
    }
    else if(comando == "TEST_SIRENA") {
      // Test de sirena por 2 segundos
      digitalWrite(PIN_SIRENA, HIGH);
      delay(2000);
      digitalWrite(PIN_SIRENA, LOW);
      Serial.println("🔊 Test de sirena completado");
    }
  }
}

void registrarNodoMaestro() {
  if(!wifi_conectado) return;

  HTTPClient http;
  WiFiClient client;

  http.begin(client, "http://" + String(ESP32_MASTER_IP) + "/api/registrar-nodo");
  http.addHeader("Content-Type", "application/json");

  DynamicJsonDocument doc(256);
  doc["node_id"] = NODE_ID;
  doc["location"] = NODE_LOCATION;
  doc["ip"] = WiFi.localIP().toString();
  doc["tipo"] = "ESP8266_SENSOR_NODE";
  doc["sensores"] = "PIR,DOOR,DHT22,PANIC";
  doc["version"] = "1.0";

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);

  if(httpResponseCode == 200) {
    Serial.println("✅ Nodo registrado con ESP32 maestro");
  } else {
    Serial.println("⚠️ No se pudo registrar con ESP32 maestro");
  }

  http.end();
}

void configurarServidorWeb() {
  // Página principal del nodo
  server.on("/", HTTP_GET, [](){
    server.send(200, "text/html", generarPaginaWebNodo());
  });

  // API - Estado del nodo
  server.on("/status", HTTP_GET, [](){
    DynamicJsonDocument doc(512);
    doc["node_id"] = NODE_ID;
    doc["location"] = NODE_LOCATION;
    doc["activo"] = nodo_activo;
    doc["alarma"] = modo_alarma;
    doc["wifi_conectado"] = wifi_conectado;
    doc["wifi_rssi"] = WiFi.RSSI();
    doc["sensores"]["pir"] = sensores.pir_detectado;
    doc["sensores"]["puerta"] = sensores.puerta_abierta;
    doc["sensores"]["temperatura"] = sensores.temperatura;
    doc["sensores"]["humedad"] = sensores.humedad;
    doc["sensores"]["boton_panico"] = sensores.boton_panico;
    doc["memoria_libre"] = ESP.getFreeHeap();
    doc["uptime"] = millis();

    String json;
    serializeJson(doc, json);
    server.send(200, "application/json", json);
  });

  // Control del nodo
  server.on("/control", HTTP_POST, [](){
    if(server.hasArg("accion")) {
      String accion = server.arg("accion");

      if(accion == "activar") {
        nodo_activo = true;
        server.send(200, "text/plain", "Nodo activado");
      }
      else if(accion == "desactivar") {
        nodo_activo = false;
        modo_alarma = false;
        digitalWrite(PIN_SIRENA, LOW);
        digitalWrite(PIN_LED_ALARM, LOW);
        server.send(200, "text/plain", "Nodo desactivado");
      }
      else if(accion == "test_sirena") {
        digitalWrite(PIN_SIRENA, HIGH);
        delay(1000);
        digitalWrite(PIN_SIRENA, LOW);
        server.send(200, "text/plain", "Test sirena completado");
      }
      else if(accion == "reset_alarma") {
        modo_alarma = false;
        digitalWrite(PIN_SIRENA, LOW);
        digitalWrite(PIN_LED_ALARM, LOW);
        server.send(200, "text/plain", "Alarma reseteada");
      }
    }
  });

  server.begin();
  Serial.println("🌐 Servidor web del nodo iniciado");
}

String generarPaginaWebNodo() {
  String html = "<!DOCTYPE html><html><head>";
  html += "<title>🏠 Nodo Sensor - " + String(NODE_ID) + "</title>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<style>";
  html += "body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #2c3e50, #3498db); color: white; }";
  html += ".container { max-width: 800px; margin: 0 auto; }";
  html += ".header { text-align: center; margin-bottom: 30px; }";
  html += ".card { background: rgba(255,255,255,0.1); padding: 20px; margin: 15px 0; border-radius: 10px; backdrop-filter: blur(10px); }";
  html += ".sensor { display: flex; justify-content: space-between; align-items: center; margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 5px; }";
  html += ".status-on { color: #2ecc71; font-weight: bold; }";
  html += ".status-off { color: #e74c3c; font-weight: bold; }";
  html += ".status-alarm { color: #f39c12; font-weight: bold; animation: blink 1s infinite; }";
  html += "@keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.3; } }";
  html += ".btn { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }";
  html += ".btn:hover { background: #2980b9; }";
  html += ".btn-danger { background: #e74c3c; }";
  html += ".btn-warning { background: #f39c12; }";
  html += ".btn-success { background: #27ae60; }";
  html += "</style>";
  html += "<script>";
  html += "function actualizarDatos() {";
  html += "  fetch('/status').then(r => r.json()).then(data => {";
  html += "    document.getElementById('pir-status').className = data.sensores.pir ? 'status-alarm' : 'status-off';";
  html += "    document.getElementById('pir-status').innerHTML = data.sensores.pir ? 'DETECTADO' : 'Normal';";
  html += "    document.getElementById('puerta-status').className = data.sensores.puerta ? 'status-alarm' : 'status-on';";
  html += "    document.getElementById('puerta-status').innerHTML = data.sensores.puerta ? 'ABIERTA' : 'Cerrada';";
  html += "    document.getElementById('temp-value').innerHTML = data.sensores.temperatura.toFixed(1) + '°C';";
  html += "    document.getElementById('hum-value').innerHTML = data.sensores.humedad.toFixed(1) + '%';";
  html += "    document.getElementById('nodo-status').className = data.activo ? 'status-on' : 'status-off';";
  html += "    document.getElementById('nodo-status').innerHTML = data.activo ? 'ACTIVO' : 'INACTIVO';";
  html += "    document.getElementById('alarma-status').className = data.alarma ? 'status-alarm' : 'status-on';";
  html += "    document.getElementById('alarma-status').innerHTML = data.alarma ? 'ALARMA!' : 'Normal';";
  html += "    document.getElementById('wifi-rssi').innerHTML = data.wifi_rssi + ' dBm';";
  html += "    document.getElementById('memoria').innerHTML = Math.round(data.memoria_libre / 1024) + ' KB';";
  html += "    document.getElementById('uptime').innerHTML = Math.round(data.uptime / 1000) + ' seg';";
  html += "  });";
  html += "}";
  html += "function enviarComando(accion) {";
  html += "  fetch('/control', { method: 'POST', headers: {'Content-Type': 'application/x-www-form-urlencoded'}, body: 'accion=' + accion })";
  html += "  .then(r => r.text()).then(data => { alert(data); actualizarDatos(); });";
  html += "}";
  html += "setInterval(actualizarDatos, 2000);";
  html += "window.onload = actualizarDatos;";
  html += "</script>";
  html += "</head><body>";

  html += "<div class='container'>";
  html += "<div class='header'>";
  html += "<h1>🏠 Nodo Sensor Remoto</h1>";
  html += "<p><strong>ID:</strong> " + String(NODE_ID) + "</p>";
  html += "<p><strong>Ubicación:</strong> " + String(NODE_LOCATION) + "</p>";
  html += "</div>";

  // Estado del nodo
  html += "<div class='card'>";
  html += "<h3>📊 Estado del Nodo</h3>";
  html += "<div class='sensor'>";
  html += "<span>Sistema:</span>";
  html += "<span id='nodo-status' class='status-on'>ACTIVO</span>";
  html += "</div>";
  html += "<div class='sensor'>";
  html += "<span>Alarma:</span>";
  html += "<span id='alarma-status' class='status-on'>Normal</span>";
  html += "</div>";
  html += "<div class='sensor'>";
  html += "<span>WiFi:</span>";
  html += "<span id='wifi-rssi'>-50 dBm</span>";
  html += "</div>";
  html += "<div class='sensor'>";
  html += "<span>Memoria libre:</span>";
  html += "<span id='memoria'>0 KB</span>";
  html += "</div>";
  html += "<div class='sensor'>";
  html += "<span>Uptime:</span>";
  html += "<span id='uptime'>0 seg</span>";
  html += "</div>";
  html += "</div>";

  // Sensores
  html += "<div class='card'>";
  html += "<h3>🔍 Sensores</h3>";
  html += "<div class='sensor'>";
  html += "<span>🚶 PIR (Movimiento):</span>";
  html += "<span id='pir-status' class='status-off'>Normal</span>";
  html += "</div>";
  html += "<div class='sensor'>";
  html += "<span>🚪 Sensor Puerta:</span>";
  html += "<span id='puerta-status' class='status-on'>Cerrada</span>";
  html += "</div>";
  html += "<div class='sensor'>";
  html += "<span>🌡️ Temperatura:</span>";
  html += "<span id='temp-value'>0°C</span>";
  html += "</div>";
  html += "<div class='sensor'>";
  html += "<span>💧 Humedad:</span>";
  html += "<span id='hum-value'>0%</span>";
  html += "</div>";
  html += "</div>";

  // Controles
  html += "<div class='card'>";
  html += "<h3>🎮 Controles</h3>";
  html += "<button class='btn btn-success' onclick='enviarComando(\"activar\")'>✅ Activar Nodo</button>";
  html += "<button class='btn btn-danger' onclick='enviarComando(\"desactivar\")'>❌ Desactivar Nodo</button>";
  html += "<button class='btn btn-warning' onclick='enviarComando(\"test_sirena\")'>🔊 Test Sirena</button>";
  html += "<button class='btn' onclick='enviarComando(\"reset_alarma\")'>🔄 Reset Alarma</button>";
  html += "</div>";

  html += "</div>";
  html += "</body></html>";

  return html;
}

void actualizarLEDsEstado() {
  // LED de estado (parpadea si el nodo está activo)
  static unsigned long ultimo_parpadeo_status = 0;
  if(nodo_activo) {
    if(millis() - ultimo_parpadeo_status > 1000) {
      digitalWrite(PIN_LED_STATUS, !digitalRead(PIN_LED_STATUS));
      ultimo_parpadeo_status = millis();
    }
  } else {
    digitalWrite(PIN_LED_STATUS, LOW);
  }

  // LED WiFi (sólido si conectado, apagado si no)
  digitalWrite(PIN_LED_WIFI, wifi_conectado ? HIGH : LOW);
}

void secuenciaInicioLEDs() {
  // Secuencia de LEDs de inicio
  for(int i = 0; i < 3; i++) {
    digitalWrite(PIN_LED_STATUS, HIGH);
    digitalWrite(PIN_LED_ALARM, HIGH);
    digitalWrite(PIN_LED_WIFI, HIGH);
    delay(200);
    digitalWrite(PIN_LED_STATUS, LOW);
    digitalWrite(PIN_LED_ALARM, LOW);
    digitalWrite(PIN_LED_WIFI, LOW);
    delay(200);
  }

  // Beep de confirmación
  digitalWrite(PIN_SIRENA, HIGH);
  delay(100);
  digitalWrite(PIN_SIRENA, LOW);
}
