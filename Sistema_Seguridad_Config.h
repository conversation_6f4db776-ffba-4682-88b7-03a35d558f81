/*
 * 🔧 CONFIGURACIÓN COMPLETA - SISTEMA DE SEGURIDAD INTELIGENTE
 * 
 * INSTRUCCIONES:
 * 1. Modifica los valores según tu configuración
 * 2. Incluye este archivo en cada proyecto: #include "Sistema_Seguridad_Config.h"
 * 3. Asegúrate de que todos los dispositivos usen la MISMA configuración WiFi
 */

#ifndef SISTEMA_SEGURIDAD_CONFIG_H
#define SISTEMA_SEGURIDAD_CONFIG_H

// ===== CONFIGURACIÓN WiFi (CRÍTICO - IGUAL EN TODOS LOS DISPOSITIVOS) =====
#define WIFI_SSID "Redmi Note 12 5G"           // ⚠️ CAMBIAR por tu WiFi
#define WIFI_PASSWORD "123456789"              // ⚠️ CAMBIAR por tu password

// ===== CONFIGURACIÓN DE RED =====
#define ESP32_MASTER_IP "*************"        // IP fija del ESP32 maestro
#define ESP32_CAM_IP "*************"           // IP fija del ESP32-CAM

// ===== CONFIGURACIÓN API DE IA =====
#define AI_API_URL "https://api.openai.com/v1/chat/completions"
#define AI_API_KEY "sk-tu-openai-key-aqui"     // ⚠️ CAMBIAR por tu API key
#define AI_MODEL "gpt-3.5-turbo"

// ===== CONFIGURACIÓN DE SEGURIDAD =====
#define CODIGO_MAESTRO "1234"                  // Código para abrir puerta manualmente
#define TIMEOUT_PUERTA 10000                   // Tiempo puerta abierta (10 segundos)
#define MAX_INTENTOS_FALLIDOS 3                // Intentos antes de activar alerta

// ===== CONFIGURACIÓN DE SENSORES =====
#define DISTANCIA_ACTIVACION 50                // Distancia HC-SR04 para activar (cm)
#define SENSIBILIDAD_MOVIMIENTO 30             // Umbral detección movimiento cámara

// ===== CONFIGURACIÓN DE USUARIOS POR DEFECTO =====
#define USUARIO_ADMIN_NOMBRE "Admin"
#define USUARIO_ADMIN_RFID "12345678"
#define USUARIO_ADMIN_HUELLA "001"

// ===== CONFIGURACIÓN DE PINES ESP32 MAESTRO =====
#define PIN_BUZZER 2
#define PIN_LED_VERDE 4
#define PIN_LED_ROJO 5
#define PIN_LED_AZUL 18
#define PIN_RELE_PUERTA 19
#define PIN_BOTON_PANICO 21

// ===== CONFIGURACIÓN DE PINES ARDUINO UNO =====
#define PIN_IR1 2
#define PIN_IR2 3
#define PIN_ULTRASONICO_ECHO 6
#define PIN_ULTRASONICO_TRIG 7
#define PIN_RFID_RST 9
#define PIN_RFID_SS 10

// ===== CONFIGURACIÓN ESP32-CAM =====
#define PIN_LED_FLASH 4
#define CALIDAD_FOTO_JPEG 10                   // Calidad JPEG (0-63, menor=mejor)

// ===== CONFIGURACIÓN AVANZADA =====
#define DEBUG_MODE true                        // Habilitar mensajes debug
#define INTERVALO_LECTURA_SENSORES 100         // Lectura sensores cada 100ms
#define MAX_EVENTOS_MEMORIA 100                // Máximo eventos en memoria

#endif // SISTEMA_SEGURIDAD_CONFIG_H
