/*
 * ESP8266 WiFi Repeater con Limitador de Datos
 * Versión simplificada para Arduino IDE
 * 
 * INSTRUCCIONES:
 * 1. Cambia las credenciales WiFi en la sección de configuración
 * 2. Ajusta los límites según tus necesidades
 * 3. Compila y sube al ESP8266
 * 4. Conéctate al hotspot y ve a http://***********
 */

#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <WiFiClient.h>
#include <EEPROM.h>

// ===== CONFIGURACIÓN - CAMBIAR ESTOS VALORES =====
// WiFi origen (al que se conectará el ESP8266)
const char* ssid_origen = "Redmi Note 12 5G";        // ⚠️ CAMBIAR por tu WiFi
const char* password_origen = "123456789";            // ⚠️ CAMBIAR por tu password

// Punto de acceso (hotspot que creará el ESP8266)
const char* ap_ssid = "Paimon Wifi";                 // Nombre del hotspot
const char* ap_password = "123456789";               // Password del hotspot (mínimo 8 caracteres)

// Configuración de límites
const unsigned long LIMITE_MB = 100;                 // Límite en MB por cliente
const unsigned long LIMITE_BYTES = LIMITE_MB * 1024 * 1024; // Conversión a bytes
const int MAX_CLIENTES = 4;                          // Máximo número de clientes

// Configuración del sistema
const int WEB_SERVER_PORT = 80;
const int SERIAL_BAUD_RATE = 115200;
const int EEPROM_SIZE = 512;
const unsigned long INTERVALO_VERIFICACION = 5000;   // 5 segundos
const int WIFI_CONNECT_TIMEOUT = 20;                 // 20 intentos
const unsigned long AUTO_REFRESH_WEB = 10000;        // 10 segundos
const bool SIMULATE_DATA_USAGE = true;
const int MIN_BYTES_PER_CHECK = 1024;                // 1 KB
const int MAX_BYTES_PER_CHECK = 8192;                // 8 KB

// Configuración de red AP
const IPAddress AP_IP_ADDRESS(192, 168, 4, 1);
const IPAddress AP_GATEWAY(192, 168, 4, 1);
const IPAddress AP_SUBNET(255, 255, 255, 0);

// ===== FIN DE CONFIGURACIÓN =====

// Servidor web para notificaciones
ESP8266WebServer server(WEB_SERVER_PORT);

// Estructura para almacenar datos de clientes
struct ClienteData {
  IPAddress ip;
  unsigned long bytesUsados;
  unsigned long ultimaActividad;
  bool activo;
  bool notificado;
};

ClienteData clientes[MAX_CLIENTES];
unsigned long ultimaVerificacion = 0;

// Variables de estado
bool wifiConectado = false;
bool apActivo = false;

// Declaraciones de funciones
void conectarWiFiOrigen();
void configurarAP();
void configurarServidor();
void monitorearClientes();
int encontrarOCrearCliente(IPAddress ip);
void notificarLimiteExcedido(IPAddress ip, int indice);
void desconectarCliente(IPAddress ip);
void reiniciarEstadisticas();
String generarPaginaEstado();
String generarEstadisticasJSON();
String generarPaginaLimite();

void setup() {
  Serial.begin(SERIAL_BAUD_RATE);
  delay(1000);
  
  Serial.println("\n=== ESP8266 WiFi Repeater con Limitador ===");
  Serial.println("Configuración:");
  Serial.print("- WiFi origen: ");
  Serial.println(ssid_origen);
  Serial.print("- Hotspot: ");
  Serial.println(ap_ssid);
  Serial.print("- Límite por cliente: ");
  Serial.print(LIMITE_MB);
  Serial.println(" MB");
  Serial.print("- Máximo clientes: ");
  Serial.println(MAX_CLIENTES);
  Serial.println();
  
  // Inicializar EEPROM para persistencia de datos
  EEPROM.begin(EEPROM_SIZE);
  
  // Inicializar estructura de clientes
  for(int i = 0; i < MAX_CLIENTES; i++) {
    clientes[i].ip = IPAddress(0, 0, 0, 0);
    clientes[i].bytesUsados = 0;
    clientes[i].ultimaActividad = 0;
    clientes[i].activo = false;
    clientes[i].notificado = false;
  }
  
  // Conectar a WiFi origen
  conectarWiFiOrigen();
  
  // Configurar punto de acceso
  configurarAP();
  
  // Configurar servidor web
  configurarServidor();
  
  Serial.println("Sistema iniciado correctamente");
  Serial.println("Conéctate al hotspot y ve a: http://***********");
  Serial.println("===============================================");
}

void loop() {
  // Manejar servidor web
  server.handleClient();
  
  // Verificar conexión WiFi origen
  if(WiFi.status() != WL_CONNECTED) {
    if(wifiConectado) {
      Serial.println("Conexión WiFi perdida, reintentando...");
      wifiConectado = false;
    }
    conectarWiFiOrigen();
  }
  
  // Monitorear clientes y uso de datos
  if(millis() - ultimaVerificacion > INTERVALO_VERIFICACION) {
    monitorearClientes();
    ultimaVerificacion = millis();
  }
  
  delay(100);
}

void conectarWiFiOrigen() {
  if(WiFi.status() == WL_CONNECTED) return;
  
  Serial.print("Conectando a WiFi origen: ");
  Serial.println(ssid_origen);
  
  WiFi.mode(WIFI_AP_STA); // Modo híbrido: Station + Access Point
  WiFi.begin(ssid_origen, password_origen);
  
  int intentos = 0;
  while(WiFi.status() != WL_CONNECTED && intentos < WIFI_CONNECT_TIMEOUT) {
    delay(500);
    Serial.print(".");
    intentos++;
  }
  
  if(WiFi.status() == WL_CONNECTED) {
    wifiConectado = true;
    Serial.println("\nWiFi origen conectado!");
    Serial.print("IP asignada: ");
    Serial.println(WiFi.localIP());
  } else {
    Serial.println("\nError: No se pudo conectar al WiFi origen");
    Serial.println("Verifica SSID y password en la configuración");
    wifiConectado = false;
  }
}

void configurarAP() {
  Serial.print("Configurando punto de acceso: ");
  Serial.println(ap_ssid);
  
  // Configurar IP del AP
  IPAddress local_IP = AP_IP_ADDRESS;
  IPAddress gateway = AP_GATEWAY;
  IPAddress subnet = AP_SUBNET;
  
  WiFi.softAPConfig(local_IP, gateway, subnet);
  
  // Iniciar punto de acceso
  bool resultado = WiFi.softAP(ap_ssid, ap_password, 1, 0, MAX_CLIENTES);
  
  if(resultado) {
    apActivo = true;
    Serial.println("Punto de acceso iniciado correctamente");
    Serial.print("IP del AP: ");
    Serial.println(WiFi.softAPIP());
    Serial.print("Máximo de clientes: ");
    Serial.println(MAX_CLIENTES);
  } else {
    Serial.println("Error: No se pudo iniciar el punto de acceso");
    apActivo = false;
  }
}

void configurarServidor() {
  // Página principal con información del sistema
  server.on("/", []() {
    String html = generarPaginaEstado();
    server.send(200, "text/html", html);
  });
  
  // API para obtener estadísticas en JSON
  server.on("/stats", []() {
    String json = generarEstadisticasJSON();
    server.send(200, "application/json", json);
  });
  
  // Página de notificación de límite excedido
  server.on("/limite", []() {
    String html = generarPaginaLimite();
    server.send(200, "text/html", html);
  });
  
  // Reiniciar estadísticas (solo para administración)
  server.on("/reset", []() {
    reiniciarEstadisticas();
    server.send(200, "text/plain", "Estadísticas reiniciadas");
  });
  
  server.begin();
  Serial.println("Servidor web iniciado en puerto 80");
}

void monitorearClientes() {
  // Obtener lista de clientes conectados
  struct station_info *stat_info = wifi_softap_get_station_info();

  // Marcar todos los clientes como inactivos inicialmente
  for(int i = 0; i < MAX_CLIENTES; i++) {
    clientes[i].activo = false;
  }

  // Procesar clientes conectados
  while(stat_info != NULL) {
    IPAddress clienteIP(stat_info->ip.addr);

    // Buscar o crear entrada para este cliente
    int indice = encontrarOCrearCliente(clienteIP);

    if(indice >= 0) {
      clientes[indice].activo = true;
      clientes[indice].ultimaActividad = millis();

      // Simular medición de datos (en un caso real, esto vendría del router/gateway)
      if(SIMULATE_DATA_USAGE) {
        clientes[indice].bytesUsados += random(MIN_BYTES_PER_CHECK, MAX_BYTES_PER_CHECK);
      }

      // Verificar si excedió el límite
      if(clientes[indice].bytesUsados >= LIMITE_BYTES) {
        if(!clientes[indice].notificado) {
          notificarLimiteExcedido(clienteIP, indice);
          clientes[indice].notificado = true;
        }
        // Desconectar cliente (esto requiere funcionalidad específica del ESP)
        desconectarCliente(clienteIP);
      }

      // Mostrar estadísticas en Serial cada 30 segundos
      static unsigned long ultimoReporte = 0;
      if(millis() - ultimoReporte > 30000) {
        Serial.print("Cliente ");
        Serial.print(clienteIP);
        Serial.print(" - Datos usados: ");
        Serial.print(clientes[indice].bytesUsados / 1024);
        Serial.print(" KB / ");
        Serial.print(LIMITE_MB);
        Serial.println(" MB");
        ultimoReporte = millis();
      }
    }

    stat_info = STAILQ_NEXT(stat_info, next);
  }

  wifi_softap_free_station_info();
}

int encontrarOCrearCliente(IPAddress ip) {
  // Buscar cliente existente
  for(int i = 0; i < MAX_CLIENTES; i++) {
    if(clientes[i].ip == ip) {
      return i;
    }
  }

  // Buscar slot libre para nuevo cliente
  for(int i = 0; i < MAX_CLIENTES; i++) {
    if(clientes[i].ip == IPAddress(0, 0, 0, 0)) {
      clientes[i].ip = ip;
      clientes[i].bytesUsados = 0;
      clientes[i].ultimaActividad = millis();
      clientes[i].activo = true;
      clientes[i].notificado = false;

      Serial.print("Nuevo cliente registrado: ");
      Serial.println(ip);
      return i;
    }
  }

  return -1; // No hay slots disponibles
}

void notificarLimiteExcedido(IPAddress ip, int indice) {
  Serial.print("¡LÍMITE EXCEDIDO! Cliente ");
  Serial.print(ip);
  Serial.print(" ha usado ");
  Serial.print(clientes[indice].bytesUsados / (1024 * 1024));
  Serial.print(" MB de ");
  Serial.print(LIMITE_MB);
  Serial.println(" MB permitidos");
}

void desconectarCliente(IPAddress ip) {
  Serial.print("Desconectando cliente por límite excedido: ");
  Serial.println(ip);

  // Marcar cliente para desconexión
  for(int i = 0; i < MAX_CLIENTES; i++) {
    if(clientes[i].ip == ip) {
      // Resetear datos del cliente
      clientes[i].ip = IPAddress(0, 0, 0, 0);
      clientes[i].bytesUsados = 0;
      clientes[i].activo = false;
      clientes[i].notificado = false;
      break;
    }
  }
}

void reiniciarEstadisticas() {
  Serial.println("Reiniciando estadísticas de todos los clientes...");

  for(int i = 0; i < MAX_CLIENTES; i++) {
    clientes[i].bytesUsados = 0;
    clientes[i].notificado = false;
  }

  Serial.println("Estadísticas reiniciadas");
}
